{
  // pages.json
  "easycom": {
    // 是否开启自动扫描
    "autoscan": true,
    "custom": {
      // uni-ui 规则如下配置
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
      "^Ktt(.*)": "@/components/Ktt$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/search/search",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTextStyle": "black"
      }
    },
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom", // 隐藏默认导航
        "navigationBarTextStyle": "white" // 设置导航栏字体颜色为白色
      }
    },
    {
      "path": "pages/my/my",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/cart/cart",
      "style": {
        "navigationBarTitleText": "购物车"
      }
    },
    {
      "path": "pages/category/category",
      "style": {
        "navigationBarTitleText": "分类"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/goods/goods",
      "style": {
        "navigationBarTitleText": "商品详情"
      }
    },
    {
      "path": "pages/activity-products/index",
      "style": {
        "navigationBarTitleText": "活动商品",
        "enablePullDownRefresh": true,
        "onReachBottomDistance": 50
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  // 设置 TabBar
  "tabBar": {
    "color": "#333",
    "selectedColor": "#27ba9b",
    "backgroundColor": "#fff",
    "borderStyle": "white",
    "list": [
      {
        "text": "首页",
        "pagePath": "pages/index/index",
        "iconPath": "static/tabs/home_default.png",
        "selectedIconPath": "static/tabs/home_selected.png"
      },
      {
        "text": "分类",
        "pagePath": "pages/category/category",
        "iconPath": "static/tabs/category_default.png",
        "selectedIconPath": "static/tabs/category_selected.png"
      },
      {
        "text": "购物车",
        "pagePath": "pages/cart/cart",
        "iconPath": "static/tabs/cart_default.png",
        "selectedIconPath": "static/tabs/cart_selected.png"
      },
      {
        "text": "我的",
        "pagePath": "pages/my/my",
        "iconPath": "static/tabs/user_default.png",
        "selectedIconPath": "static/tabs/user_selected.png"
      }
    ]
  },
  "subPackages": [
    {
      "root": "pagesMember",
      "pages": [
        {
          "path": "settings/settings",
          "style": {
            "navigationBarTitleText": "设置"
          }
        },
        {
          "path": "profile/profile",
          "style": {
            "navigationBarTitleText": "会员信息",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "address/address",
          "style": {
            "navigationBarTitleText": "地址管理页"
          }
        },
        {
          "path": "address-form/address-form",
          "style": {
            "navigationBarTitleText": "地址表单页"
          }
        },
        {
          "path": "coupon/coupon",
          "style": {
            "navigationBarTitleText": "我的优惠券",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "coupon/center",
          "style": {
            "navigationBarTitleText": "领券中心",
            "enablePullDownRefresh": true
          }
        }
      ]
    },
    {
      "root": "pagesOrder",
      "pages": [
        {
          "path": "create/create",
          "style": {
            "navigationBarTitleText": "填写订单页"
          }
        },
        {
          "path": "detail/detail",
          "style": {
            "navigationBarTitleText": "订单详情页"
          }
        },
        {
          "path": "list/list",
          "style": {
            "navigationBarTitleText": "订单列表页"
          }
        },
        {
          "path": "payment/payment",
          "style": {
            "navigationBarTitleText": "支付成功页"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    // 分包预下载规则
    "pages/my/my": {
      // 进入小程序的my.vue我的页面时
      "network": "all", // all表示所有网络
      "packages": [
        "pagesMember"
      ] // 进入my.vue我的页面后预下载pagesMember分包
    }
  }
}