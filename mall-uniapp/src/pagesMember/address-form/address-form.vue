<script setup lang="ts">
import {
  getMemberAddressByIdAPI,
  postMemberAddressAPI,
  putMemberAddressByIdAPI,
} from '@/services/address'
import type { AddressParams } from '@/types/address'
import { useMemberStore } from '@/stores'
const mem = useMemberStore()
import { ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 表单数据
const form = ref({
  id: 0,
  // 会员id
  userId: 0,
  /** 收货人姓名 */
  userName: '',
  /** 联系方式，手机号码 */
  telNumber: '',
  // 省市区(前端展示)
  fullLocation: '',
  /** 省份 */
  province: '',
  /** 城市*/
  city: '',
  /** 区/县 */
  district: '',
  /** 详细地址 */
  detail: '',
  /** 默认地址，1为是，0为否 */
  isDefault: 0,
  /** 纬度 */
  latitude: 0,
  /** 经度 */
  longitude: 0,
})

// 获取页面参数。获取从上一个页面传递过来的id参数。
const query = defineProps<{
  id?: number
}>()

// 腾讯地图Key
const TENCENT_MAP_KEY = 'AP2BZ-UCN6H-2VLDZ-WEXYE-ILZW5-CQFFK'

// 地图相关状态
const mapCenter = ref<any>({
  latitude: 39.9042,
  longitude: 116.4074,
})
const markers = ref<any[]>([])
const mapReady = ref(true)
const mapContext = ref<any>(null)

// 附近地址数据
const nearbyPlaces = ref<any[]>([])
const selectedPlaceIndex = ref(-1)
const showCustomMarker = ref(false)
const showNearbyList = ref(false) // 控制是否显示附近地址列表
const showForm = ref(true) // 控制是否显示表单
const searchKeyword = ref('') // 搜索关键词

// 当前城市
const currentCity = ref('定位中...')

// 获取IP定位城市
async function fetchCityByIP() {
  try {
    console.log('开始IP定位...')
    // 使用完整的HTTPS URL，确保不被拦截器错误处理
    const url = `https://apis.map.qq.com/ws/location/v1/ip?key=${TENCENT_MAP_KEY}&output=json`
    console.log('请求URL:', url)

    const res = await uni.request({
      url: url,
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
      },
    })

    const data = res.data as any
    console.log('IP定位响应:', data)

    // 根据API文档检查响应状态
    if (res.statusCode === 200 && data.status === 0) {
      // 根据API文档解析响应数据
      const result = data.result
      console.log('定位结果:', result)

      // 获取城市信息（仅用于显示，不赋值给表单）
      const adInfo = result.ad_info
      currentCity.value = adInfo.city || adInfo.province || '未知'
      console.log('当前城市:', currentCity.value)

      // 获取坐标信息
      const location = result.location
      const lat = location.lat
      const lng = location.lng
      console.log('定位坐标:', { lat, lng })

      // 更新地图中心点
      mapCenter.value = {
        latitude: lat,
        longitude: lng,
      }

      // 只更新表单坐标，不赋值地区和详细地址
      form.value.latitude = lat
      form.value.longitude = lng

      // 不自动赋值省市区信息
      // form.value.province = adInfo.province
      // form.value.city = adInfo.city
      // form.value.district = adInfo.district
      // form.value.fullLocation = `${adInfo.province} ${adInfo.city} ${adInfo.district}`

      // 不自动赋值详细地址
      // form.value.detail = ''

      // 更新地图标记
      updateMarkers(lat, lng, nearbyPlaces.value, selectedPlaceIndex.value)

      // 设置地图为就绪状态
      mapReady.value = true

      // 定位成功后不自动请求附近地址，等用户手动搜索
      console.log('IP定位成功完成')
    } else {
      console.log('IP定位失败，状态码:', data.status, '错误信息:', data.message)
      currentCity.value = '定位失败'
      mapReady.value = false

      // 使用默认坐标（北京）
      mapCenter.value = {
        latitude: 39.9042,
        longitude: 116.4074,
      }
      form.value.latitude = 39.9042
      form.value.longitude = 116.4074

      updateMarkers(39.9042, 116.4074, nearbyPlaces.value, selectedPlaceIndex.value)
    }
  } catch (e) {
    console.error('IP定位异常:', e)
    currentCity.value = '定位异常'
    mapReady.value = false

    // 使用默认坐标（北京）
    mapCenter.value = {
      latitude: 39.9042,
      longitude: 116.4074,
    }
    form.value.latitude = 39.9042
    form.value.longitude = 116.4074

    updateMarkers(39.9042, 116.4074, nearbyPlaces.value, selectedPlaceIndex.value)
  }
}

// 搜索周边地址
async function searchNearbyPlaces(keyword = '') {
  try {
    console.log('开始搜索周边地址...', keyword ? `关键词: ${keyword}` : '')

    // 构建搜索URL，如果有关键词则使用关键词搜索，否则使用周边搜索
    let url = ''
    if (keyword && keyword.trim()) {
      // 关键词搜索
      url = `https://apis.map.qq.com/ws/place/v1/search?keyword=${encodeURIComponent(
        keyword,
      )}&boundary=nearby(${form.value.latitude},${
        form.value.longitude
      },5000)&page_size=10&page_index=1&key=${TENCENT_MAP_KEY}&output=json`
    } else {
      // 周边搜索
      url = `https://apis.map.qq.com/ws/place/v1/explore?boundary=nearby(${form.value.latitude},${form.value.longitude},1000)&policy=1&page_size=10&page_index=1&key=${TENCENT_MAP_KEY}&output=json`
    }

    console.log('搜索URL:', url)

    const res = await uni.request({
      url: url,
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
      },
    })

    const data = res.data as any
    console.log('搜索响应:', data)

    if (res.statusCode === 200 && data.status === 0) {
      // 根据API文档解析响应数据
      const places = data.data || []
      nearbyPlaces.value = places
      console.log('找到地址:', places.length, '个')

      // 更新地图标记，显示附近地址
      updateMarkers(
        mapCenter.value.latitude,
        mapCenter.value.longitude,
        places,
        selectedPlaceIndex.value,
      )

      // 显示搜索结果提示
      if (places.length > 0) {
        uni.showToast({
          title: `找到${places.length}个地址`,
          icon: 'success',
          duration: 2000,
        })
      } else {
        uni.showToast({
          title: '未找到相关地址',
          icon: 'none',
          duration: 2000,
        })
      }

      return Promise.resolve(places)
    } else {
      console.log('搜索失败，状态码:', data.status, '错误信息:', data.message)
      nearbyPlaces.value = []
      uni.showToast({
        title: '搜索失败',
        icon: 'none',
      })
      return Promise.reject(new Error(data.message || '搜索失败'))
    }
  } catch (error) {
    console.error('搜索地址失败:', error)
    nearbyPlaces.value = []
    uni.showToast({
      title: '搜索失败',
      icon: 'none',
    })
    return Promise.reject(error)
  }
}

// 选择周边地址
function selectPlace(idx: number) {
  selectedPlaceIndex.value = idx
  const place = nearbyPlaces.value[idx]
  if (place) {
    // 不更新地图中心点，保持用户当前位置
    // mapCenter.value = {
    //   latitude: place.location.lat,
    //   longitude: place.location.lng,
    // }

    // 只更新表单坐标（用于保存）
    form.value.latitude = place.location.lat
    form.value.longitude = place.location.lng
    form.value.detail = place.title

    // 根据选择的地址更新省市区信息
    updateAddressFromPlace(place)

    // 不更新地图标记，保持当前位置标记
    // updateMarkers(
    //   mapCenter.value.latitude,
    //   mapCenter.value.longitude,
    //   nearbyPlaces.value,
    //   idx
    // )

    // 隐藏地图上的地址列表，显示表单
    showNearbyList.value = false
    showForm.value = true

    // 显示选择成功提示
    uni.showToast({
      title: '地址已选择',
      icon: 'success',
      duration: 1500,
    })
  }
}

// 根据选择的地址更新省市区信息
function updateAddressFromPlace(place: any) {
  try {
    // 尝试从地址中解析省市区信息
    const address = place.address || ''
    const title = place.title || ''

    // 如果地址包含省市区信息，尝试解析
    if (address.includes('省') || address.includes('市') || address.includes('区')) {
      // 简单的地址解析逻辑
      const provinceMatch = address.match(/(.+?省)/)
      const cityMatch = address.match(/(.+?市)/)
      const districtMatch = address.match(/(.+?区|.+?县)/)

      if (provinceMatch) form.value.province = provinceMatch[1]
      if (cityMatch) form.value.city = cityMatch[1]
      if (districtMatch) form.value.district = districtMatch[1]

      // 更新完整地址显示
      form.value.fullLocation = `${form.value.province || ''} ${form.value.city || ''} ${
        form.value.district || ''
      }`.trim()
    } else {
      // 如果无法解析，使用标题作为详细地址
      form.value.detail = title
    }
  } catch (error) {
    console.error('地址解析失败:', error)
    // 如果解析失败，至少设置详细地址
    form.value.detail = place.title
  }
}

// 手动搜索周边地址
function onSearchNearby() {
  // 检查是否有有效的坐标
  if (form.value.latitude === 0 && form.value.longitude === 0) {
    uni.showToast({
      title: '请先定位或选择位置',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 显示地址列表（在地图上显示）
  showNearbyList.value = true
  showForm.value = false

  // 显示加载提示
  uni.showLoading({
    title: '搜索中...',
  })

  // 执行搜索，传入搜索关键词
  searchNearbyPlaces(searchKeyword.value)
    .then(() => {
      uni.hideLoading()
    })
    .catch(() => {
      uni.hideLoading()
      // 搜索失败时恢复表单显示
      showForm.value = true
      showNearbyList.value = false
      uni.showToast({
        title: '搜索失败',
        icon: 'none',
        duration: 2000,
      })
    })
}

// 地图点击事件
function onMapTap(e: any) {
  const { latitude, longitude } = e.detail
  form.value.latitude = latitude
  form.value.longitude = longitude

  // 根据经纬度获取地址信息（用户手动点击时才获取）
  reverseGeocode(latitude, longitude)
}

// 地图区域变化事件（拖拽时触发）
function onMapRegionChange(e: any) {
  const { latitude, longitude } = e.detail.centerLocation
  form.value.latitude = latitude
  form.value.longitude = longitude

  // 更新地图中心点
  mapCenter.value = {
    latitude: latitude,
    longitude: longitude,
  }

  // 根据经纬度获取地址信息（用户手动拖拽时才获取）
  reverseGeocode(latitude, longitude)
}

// 逆地理编码
async function reverseGeocode(latitude: number, longitude: number) {
  try {
    console.log('开始逆地理编码...', { latitude, longitude })
    // 根据腾讯地图API文档，构建正确的请求URL
    const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${TENCENT_MAP_KEY}&output=json`
    console.log('逆地理编码URL:', url)

    const res = await uni.request({
      url: url,
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
      },
    })

    const data = res.data as any
    console.log('逆地理编码响应:', data)

    if (res.statusCode === 200 && data.status === 0) {
      // 根据API文档解析响应数据
      const result = data.result
      const addressComponent = result.address_component
      console.log('地址组件:', addressComponent)

      // 更新表单数据
      form.value.province = addressComponent.province
      form.value.city = addressComponent.city
      form.value.district = addressComponent.district
      form.value.fullLocation = `${addressComponent.province} ${addressComponent.city} ${addressComponent.district}`

      // 获取推荐地址
      if (result.formatted_addresses && result.formatted_addresses.recommend) {
        form.value.detail = result.formatted_addresses.recommend
      } else {
        form.value.detail = result.formatted_addresses?.standard || ''
      }

      console.log('地址信息更新完成:', {
        province: form.value.province,
        city: form.value.city,
        district: form.value.district,
        fullLocation: form.value.fullLocation,
        detail: form.value.detail,
      })

      updateMarkers(latitude, longitude, [], -1)
      // 显示地址获取成功提示
      uni.showToast({
        title: '地址获取成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      console.log('逆地理编码失败，状态码:', data.status, '错误信息:', data.message)
      uni.showToast({
        title: '地址获取失败',
        icon: 'none',
        duration: 1500,
      })
    }
  } catch (error) {
    console.error('逆地理编码失败:', error)
    uni.showToast({
      title: '地址获取失败',
      icon: 'none',
      duration: 1500,
    })
  }
}

// 地图加载完成
function onMapLoaded() {
  console.log('地图加载完成')
  try {
    mapContext.value = uni.createMapContext('myMap')
    console.log('地图上下文创建成功')
    updateMapCenter()
  } catch (error) {
    console.error('地图上下文创建失败:', error)
  }
}

// 更新地图中心点
function updateMapCenter() {
  if (mapContext.value) {
    try {
      mapContext.value.moveToLocation({
        latitude: form.value.latitude,
        longitude: form.value.longitude,
      })
      console.log('地图中心点更新成功:', form.value.latitude, form.value.longitude)
    } catch (error) {
      console.error('地图中心点更新失败:', error)
    }
  }
}

// 更新地图markers
function updateMarkers(lat: number, lng: number, places: any[], selectedIdx: number) {
  // 当前位置标记
  const baseMarkers = [
    {
      id: 1,
      latitude: lat,
      longitude: lng,
      iconPath: '/static/images/locate.png',
      width: 30,
      height: 30,
      callout: {
        content: '当前位置',
        color: '#FF0000',
        fontSize: 12,
        borderRadius: 4,
        padding: 5,
        display: 'ALWAYS',
        textAlign: 'center',
      },
      anchor: {
        x: 0.5,
        y: 1,
      },
    },
  ]

  // 不添加周边地址marker，只保留当前位置标记
  markers.value = baseMarkers
}

// 返回上一页
const goBack = () => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    uni.switchTab({
      url: '/pages/home/<USER>',
    })
  }
}

// 收集所在地区
const onRegionChange: any = (ev: any) => {
  form.value.fullLocation = ev.detail.value.join(' ')
  const [province, city, district] = ev.detail.code!
  Object.assign(form.value, { province, city, district })
}

// 提交表单
const onSubmit = async () => {
  form.value.userId = mem.profile.id
  if (query.id) {
    await putMemberAddressByIdAPI(form.value)
  } else {
    await postMemberAddressAPI(form.value)
  }
  uni.showToast({ icon: 'success', title: query.id ? '修改成功' : '添加成功' })
  setTimeout(() => {
    uni.navigateBack()
  }, 400)
}

// 收集是否默认收货地址
const onSwitchChange: any = (ev: any) => {
  form.value.isDefault = ev.detail.value ? 1 : 0
}

// 获取收货地址详情数据
const getMemberAddressByIdData = async () => {
  if (query.id) {
    const res = await getMemberAddressByIdAPI(query.id)
    Object.assign(form.value, res.result)
  }
}

// 选中项变化时自动高亮marker - 修复watch函数
watch(selectedPlaceIndex, (newIdx) => {
  if (mapCenter.value && nearbyPlaces.value) {
    updateMarkers(mapCenter.value.latitude, mapCenter.value.longitude, nearbyPlaces.value, newIdx)
  }
})

// 页面加载
onLoad(() => {
  console.log('页面加载开始...')
  getMemberAddressByIdData()

  // 立即初始化地图标记
  updateMarkers(mapCenter.value.latitude, mapCenter.value.longitude, [], -1)

  // 延迟执行IP定位，避免阻塞页面渲染
  setTimeout(() => {
    fetchCityByIP()
  }, 100)
})
</script>

<template>
  <view class="location-container">
    <!-- 地图区域 -->
    <view class="map-container">
      <map
        v-if="mapReady"
        class="map"
        :latitude="mapCenter.latitude"
        :longitude="mapCenter.longitude"
        :markers="markers"
        :scale="15"
        show-location
        @tap="onMapTap"
        @loaded="onMapLoaded"
        @regionchange="onMapRegionChange"
      ></map>
      <view v-else class="map-loading">定位中...</view>
    </view>

    <!-- 自定义红色标记点 -->
    <view class="custom-marker" v-if="showCustomMarker">
      <view class="marker-dot red-marker"></view>
    </view>

    <!-- 搜索区域 - 地图下方 -->
    <view class="search-section" @tap="onSearchNearby">
      <view class="search-input-container">
        <input
          class="search-input"
          placeholder="输入关键词搜索地址"
          v-model="searchKeyword"
          @confirm="onSearchNearby"
        />
        <button class="search-btn">
          <text class="search-icon">🔍</text>
        </button>
      </view>
    </view>

    <!-- 附近地址列表 -->
    <view class="nearby-vm-list" v-if="showNearbyList && nearbyPlaces.length">
      <view class="nearby-title">搜索结果</view>
      <view
        class="nearby-vm-item"
        v-for="(place, idx) in nearbyPlaces"
        :key="place.id"
        :class="{ selected: selectedPlaceIndex === idx }"
        @tap="selectPlace(idx)"
      >
        <view class="vm-list-icon">📍</view>
        <view class="vm-info">
          <view class="vm-name">{{ place.title }}</view>
          <view class="vm-addr">{{ place.address }}</view>
          <view class="vm-distance">距离：{{ place._distance.toFixed(0) }} 米</view>
        </view>
        <view v-if="selectedPlaceIndex === idx" class="vm-check">✓</view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-section" v-if="showForm">
      <view class="form-item">
        <text class="label">收货人</text>
        <input class="input" placeholder="请填写收货人姓名" v-model="form.userName" />
      </view>
      <view class="form-item">
        <text class="label">手机号码</text>
        <input class="input" placeholder="请填写收货人手机号码" v-model="form.telNumber" />
      </view>
      <view class="form-item">
        <text class="label">所在地区</text>
        <picker
          class="picker"
          mode="region"
          :value="form.fullLocation.split(' ')"
          @change="onRegionChange"
        >
          <view v-if="form.fullLocation">{{ form.fullLocation }}</view>
          <view v-else class="placeholder">请选择省/市/区(县)</view>
        </picker>
      </view>
      <view class="form-item">
        <text class="label">详细地址</text>
        <input class="input" placeholder="街道、楼牌号等信息" v-model="form.detail" />
      </view>
      <view class="form-item">
        <label class="label">设为默认地址</label>
        <switch
          class="switch"
          color="#3c69dc"
          :checked="form.isDefault === 1"
          @change="onSwitchChange"
        />
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button @tap="onSubmit" class="submit-btn">保存并使用</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page {
  background-color: #f4f4f4;
}

.location-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f4f4;
  position: relative;
}

/* 地图区域 */
.map-container {
  height: 45vh;
  width: 100%;
  margin-top: 0;
  position: relative;
  z-index: 2;
}

.map {
  width: 100%;
  height: 100%;
}

.map-loading {
  width: 100%;
  height: 45vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
  background: #f7f7f7;
}

/* 自定义红色标记点 */
.custom-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  z-index: 3;
}

.marker-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ff0000;
  box-shadow: 0 0 10rpx rgba(255, 0, 0, 0.7);
}

.red-marker {
  background-color: #ff0000;
}

/* 地图上的地址列表 */
.map-address-list {
  position: absolute;
  top: 45vh; /* 地图下方 */
  left: 0;
  width: 100%;
  height: 30vh; /* 列表高度 */
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 5;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.list-count {
  font-size: 24rpx;
  color: #999;
}

.list-scroll {
  height: 100%;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.list-item.selected {
  background-color: #f6faff;
}

.item-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 26rpx;
  color: #222;
  font-weight: bold;
}

.item-address {
  font-size: 22rpx;
  color: #666;
  margin: 4rpx 0;
}

.item-distance {
  font-size: 20rpx;
  color: #999;
}

.item-action {
  padding-left: 20rpx;
}

.use-btn {
  font-size: 24rpx;
  color: #3c69dc;
  font-weight: bold;
}

/* 附近地址列表 */
.nearby-vm-list {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  margin: 20rpx 0 0 0;
  padding: 20rpx 32rpx;
}

.nearby-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.nearby-vm-item {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 16rpx 0;
  position: relative;
  cursor: pointer;
}

.nearby-vm-item.selected {
  background: #f6faff;
}

.vm-list-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vm-info {
  flex: 1;
}

.vm-name {
  font-size: 26rpx;
  color: #222;
  font-weight: bold;
}

.vm-addr {
  font-size: 22rpx;
  color: #666;
  margin: 4rpx 0;
}

.vm-distance {
  font-size: 20rpx;
  color: #999;
}

.vm-check {
  width: 28rpx;
  height: 28rpx;
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #3c69dc;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 表单区域 */
.form-section {
  flex: 1;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: -20rpx;
  padding: 20rpx 32rpx;
  overflow-y: auto;
  position: relative;
  z-index: 3;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 96rpx;
  padding: 25rpx 10rpx 40rpx;
  background-color: #fff;
  font-size: 28rpx;
  border-bottom: 1rpx solid #ddd;
  position: relative;
  margin-bottom: 0;

  &:last-child {
    border: none;
  }

  .label {
    width: 200rpx;
    color: #333;
  }

  .input {
    flex: 1;
    display: block;
    height: 46rpx;
  }

  .switch {
    position: absolute;
    right: -20rpx;
    transform: scale(0.8);
  }

  .picker {
    flex: 1;
  }

  .placeholder {
    color: #808080;
  }
}

/* 搜索区域 - 地图下方 */
.search-section {
  background-color: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 40rpx;
  padding: 10rpx 20rpx;
  border: 1rpx solid #e0e0e0;
}

.search-input {
  flex: 1;
  height: 60rpx;
  padding: 0 10rpx;
  font-size: 28rpx;
  background: none;
  border: none;
  outline: none;
}

.search-btn {
  height: 60rpx;
  width: 60rpx;
  border-radius: 50%;
  background-color: #3c69dc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  border: none;
}

.search-icon {
  font-size: 32rpx;
  color: #fff;
}

.search-text {
  font-size: 30rpx;
}

.search-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 提交按钮 */
.submit-section {
  margin-top: 20rpx;
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
}

.submit-btn {
  height: 80rpx;
  width: 90%;
  color: #fff;
  border-radius: 80rpx;
  font-size: 30rpx;
  background-color: #3c69dc;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
