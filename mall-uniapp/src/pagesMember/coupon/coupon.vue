<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMemberStore } from '@/stores'
import { getUserCouponListAPI } from '@/services/coupon'
import type { UserCouponItem, UserCouponStatus } from '@/types/coupon'
import CouponItem from '@/components/CouponItem.vue'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 获取会员信息
const memberStore = useMemberStore()

// 页面参数（使用响应式变量存储运行时参数）
const query = ref({
  status: '',
  from: '',
  goodsId: '',
  categoryId: '',
  goodsIds: '',
  categoryIds: ''
});

// 优惠券列表
const couponList = ref<UserCouponItem[]>([])
// 加载状态
const loading = ref(false)
// 是否还有更多数据
const hasMore = ref(true)
// 当前页码
const page = ref(1)
// 每页大小
const pageSize = ref(10)

// 优惠券状态选项
const statusOptions = [
  { value: '', text: '全部' },
  { value: '0', text: '未使用' },
  { value: '1', text: '已使用' },
  { value: '2', text: '已过期' },
]

// 当前选中的状态
const currentStatus = ref('')

// 是否来自订单页面
const isFromOrder = computed(() => query.value.from === 'order')

// 选中的优惠券ID（用于订单页面选择模式）
const selectedCouponId = ref<number | null>(null)

// 获取选中的优惠券信息
const selectedCoupon = computed(() => {
  if (!selectedCouponId.value) return null
  return couponList.value.find(c => c.id === selectedCouponId.value)
})

// 获取选中优惠券的显示信息
const selectedCouponInfo = computed(() => {
  if (!selectedCoupon.value?.coupon) return null
  
  const coupon = selectedCoupon.value.coupon
  
  if (coupon.couponType === 1) { // 满减券
    return {
      amount: `¥${coupon.faceValue}`,
      desc: coupon.minSpend > 0 ? `满${coupon.minSpend}元可用` : '无门槛使用',
      name: coupon.couponName || '满减券'
    }
  } else if (coupon.couponType === 2) { // 折扣券
    return {
      amount: `${coupon.discount}折`,
      desc: coupon.minSpend > 0 ? `满${coupon.minSpend}元可用` : '无门槛使用', 
      name: coupon.couponName || '折扣券'
    }
  } else if (coupon.couponType === 3) { // 无门槛券
    return {
      amount: `¥${coupon.faceValue}`,
      desc: '无门槛使用',
      name: coupon.couponName || '无门槛券'
    }
  } else if (coupon.couponType === 4) { // 包邮券
    return {
      amount: '包邮',
      desc: coupon.minSpend > 0 ? `满${coupon.minSpend}元可用` : '无门槛包邮',
      name: coupon.couponName || '包邮券'
    }
  }
  
  return {
    amount: coupon.couponName || '优惠券',
    desc: '可使用',
    name: coupon.couponName || '优惠券'
  }
})

// 获取优惠券列表
const getCouponList = async (isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true
    
    const userId = memberStore.profile?.user.id
    if (!userId) {
      uni.showToast({ title: '请先登录', icon: 'none' })
      setTimeout(() => {
        uni.navigateTo({ url: '/pages/login/login' })
      }, 1500)
      return
    }

    const params: any = {
      userId,
      pageNum: isLoadMore ? page.value : 1,
      pageSize: pageSize.value
    }
    
    // 只有当状态不为空时，才添加useStatus参数
    if (currentStatus.value) {
      params.useStatus = Number(currentStatus.value)
    }

    // 添加商品ID和分类ID参数（用于判断优惠券是否可用）
    if (query.value.goodsId) {
      params.goodsId = Number(query.value.goodsId)
    }
    if (query.value.categoryId) {
      params.categoryId = Number(query.value.categoryId)
    }
    
    // 处理多个商品ID和分类ID（从订单页面传递过来）
    if (query.value.goodsIds) {
      const goodsIdArray = query.value.goodsIds.split(',').map(id => Number(id.trim())).filter(id => !isNaN(id))
      if (goodsIdArray.length > 0) {
        params.goodsIds = goodsIdArray
      }
    }
    
    if (query.value.categoryIds) {
      const categoryIdArray = query.value.categoryIds.split(',').map(id => Number(id.trim())).filter(id => !isNaN(id))
      if (categoryIdArray.length > 0) {
        params.categoryIds = categoryIdArray
      }
    }
    
    console.log('优惠券查询参数:', params)
    console.log('传递的商品IDs:', params.goodsIds)
    console.log('传递的分类IDs:', params.categoryIds)

    const result = await getUserCouponListAPI(params)
    if (result.code === 200) {
      if (isLoadMore) {
        couponList.value.push(...result.rows)
      } else {
        couponList.value = result.rows
      }
      
      // 判断是否还有更多数据
      hasMore.value = result.rows.length === pageSize.value
    } else {
      uni.showToast({ title: result.msg || '获取优惠券列表失败', icon: 'error' })
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    uni.showToast({ title: '获取优惠券列表失败', icon: 'error' })
  } finally {
    loading.value = false
  }
}

// 下拉刷新
const onRefresh = async () => {
  page.value = 1
  hasMore.value = true
  await getCouponList()
}

// 上拉加载更多
const onLoadMore = async () => {
  if (!hasMore.value || loading.value) return

  page.value++
  await getCouponList(true)
}

// 切换状态筛选
const onStatusChange = (status: string) => {
  currentStatus.value = status
  page.value = 1
  hasMore.value = true
  getCouponList()
}

// 将UserCouponItem转换为CouponItem组件需要的格式
const convertToCouponItemFormat = (userCoupon: UserCouponItem) => {
  if (!userCoupon.coupon) return null

  // 根据使用状态确定优惠券的claimed状态
  let claimed = false
  if (userCoupon.useStatus === 1) { // 已使用
    claimed = true
  } else if (userCoupon.useStatus === 2) { // 已过期
    claimed = true
  } else {
    claimed = false // 未使用
  }

  // 缓存时间转换结果，避免重复计算 - 修复iOS日期格式兼容性
  const startTime = userCoupon.coupon.startTime ? new Date(userCoupon.coupon.startTime.replace(/\s/g, 'T')).getTime() : 0
  const endTime = userCoupon.coupon.endTime ? new Date(userCoupon.coupon.endTime.replace(/\s/g, 'T')).getTime() : 0

  return {
    id: userCoupon.id,
    couponType: userCoupon.coupon.couponType,
    couponName: userCoupon.coupon.couponName,
    minSpend: userCoupon.coupon.minSpend || 0,
    faceValue: userCoupon.coupon.faceValue,
    discount: userCoupon.coupon.discount,
    startTime,
    endTime,
    remainCount: userCoupon.coupon.remainCount || 0,
    claimed: claimed,
    // 添加额外的状态信息用于组件内部判断
    useStatus: userCoupon.useStatus,
    // 添加必需的字段以匹配ExtendedCouponItem类型
    status: claimed ? 'used' : 'available',
    createTime: startTime,
    updateTime: endTime,
    totalCount: userCoupon.coupon.totalCount || 0
  }
}

// 转换后的优惠券列表 - 使用缓存优化性能
const convertedCouponList = computed(() => {
  if (!couponList.value.length) return []
  
  return couponList.value
    .map(convertToCouponItemFormat)
    .filter((coupon): coupon is NonNullable<typeof coupon> => coupon !== null)
})

// 处理优惠券点击事件
const handleCouponAction = (coupon: any) => {
  if (isFromOrder.value) {
    // 如果来自订单页面，则进行选择操作
    if (coupon.useStatus === 0) { // 只有未使用的优惠券可以选择
      selectedCouponId.value = selectedCouponId.value === coupon.id ? null : coupon.id
    } else {
      uni.showToast({
        title: coupon.useStatus === 1 ? '优惠券已使用' : '优惠券已过期',
        icon: 'none'
      })
    }
  } else {
    // 原有逻辑
    if (coupon.status === 'available') {
      uni.navigateTo({
        url: `/pagesOrder/create/create?couponId=${coupon.id}`
      })
    } else {
      uni.showToast({
        title: coupon.status === 'used' ? '优惠券已使用' : '优惠券已过期',
        icon: 'none'
      })
    }
  }
}

// 确认选择优惠券
const confirmSelection = () => {
  if (selectedCouponId.value) {
    const selectedCoupon = couponList.value.find(c => c.id === selectedCouponId.value)
    if (selectedCoupon) {
      // 返回到订单页面，携带选中的优惠券信息
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      if (prevPage) {
        // 通过事件总线或者页面参数传递选中的优惠券信息
        uni.$emit('couponSelected', {
          id: selectedCoupon.id,
          couponId: selectedCoupon.couponId,
          name: selectedCoupon.coupon?.couponName,
          discount: selectedCoupon.coupon?.faceValue || 0,
          minAmount: selectedCoupon.coupon?.minSpend || 0
        })
        uni.navigateBack()
      }
    }
  } else {
    uni.showToast({
      title: '请选择一张优惠券',
      icon: 'none'
    })
  }
}

onLoad((options) => {
  console.log('优惠券页面接收到的参数:', options)
  
  // 更新页面参数（从URL参数中获取）
  if (options) {
    query.value = {
      status: options.status || '',
      from: options.from || '',
      goodsId: options.goodsId || '',
      categoryId: options.categoryId || '',
      goodsIds: options.goodsIds || '',
      categoryIds: options.categoryIds || ''
    }
    
    // 设置当前状态
    if (options.status) {
      currentStatus.value = options.status
    }
  }
  
  getCouponList()
})
</script>

<template>
  <view class="viewport">
    <!-- 状态筛选 -->
    <view class="filter">
      <view
        v-for="option in statusOptions"
        :key="option.value"
        class="filter-item"
        :class="{ active: currentStatus === option.value }"
        @tap="onStatusChange(option.value)"
      >
        {{ option.text }}
      </view>
    </view>

    <!-- 优惠券列表 -->
    <scroll-view
      class="coupon-list"
      scroll-y
      refresher-enabled
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <view v-if="couponList.length === 0 && !loading" class="empty">
        <view class="empty-icon">📋</view>
        <text>暂无优惠券</text>
      </view>

      <view v-else class="coupon-items">
        <view 
          v-for="coupon in convertedCouponList"
          :key="coupon.id"
          class="coupon-wrapper"
          :class="{ 'from-order': isFromOrder }"
          @tap="handleCouponAction(coupon)"
        >
          <CouponItem
            :coupon="coupon"
            mode="owned"
            @claim="handleCouponAction"
          />
          <!-- 选择状态圆角图标 -->
          <view 
            v-if="isFromOrder && coupon.useStatus === 0"
            class="selection-indicator"
            :class="{ 'selected': selectedCouponId === coupon.id }"
          >
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>

      <view v-else-if="!hasMore && couponList.length > 0" class="no-more">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>

    <!-- 底部确认按钮（仅在从订单页面跳转时显示） -->
    <view v-if="isFromOrder" class="bottom-action" :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' }">
      <view class="action-content">
        <!-- 左侧优惠券信息显示 -->
        <view class="coupon-info">
          <view v-if="selectedCouponInfo" class="selected-coupon">
            <view class="coupon-main">
              <text class="amount-text">{{ selectedCouponInfo.amount }}</text>
              <text class="coupon-name">{{ selectedCouponInfo.name }}</text>
            </view>
            <text class="coupon-desc">{{ selectedCouponInfo.desc }}</text>
          </view>
          <text v-else class="amount-placeholder">请选择优惠券</text>
        </view>
        
        <!-- 右侧确认按钮 -->
        <view 
          class="confirm-btn"
          :class="{ 'disabled': !selectedCouponId }"
          @tap="confirmSelection"
        >
          <text class="btn-text">确认</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page {
  height: 100%;
  overflow: hidden;
}

.viewport {
  min-height: 100vh;
  background-color: #f7f7f8;
  display: flex;
  flex-direction: column;
  position: relative;
}

.filter {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;

  .filter-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666;
    border-radius: 8rpx;
    margin: 0 10rpx;

    &.active {
      background-color: #3c69dc;
      color: #fff;
    }
  }
}

.coupon-list {
  flex: 1;
  padding: 20rpx;
  height: 0;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 20rpx;
    opacity: 0.6;
  }

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.coupon-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.coupon-wrapper {
  position: relative;
  
  &.from-order {
    cursor: pointer;
  }
}

.selection-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  
  &.selected {
    background-color: #3c69dc;
    border-color: #3c69dc;
    
    .check-icon {
      color: #fff;
      font-size: 28rpx;
      font-weight: bold;
    }
  }
  
  .check-icon {
    color: transparent;
    font-size: 28rpx;
    font-weight: bold;
    transition: color 0.3s ease;
  }
}

.bottom-action {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.coupon-info {
  flex: 1;
  display: flex;
  align-items: center;
  
  .selected-coupon {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    
    .coupon-main {
      display: flex;
      align-items: center;
      gap: 16rpx;
      
      .amount-text {
        font-size: 36rpx;
        font-weight: 700;
        color: #ff4757;
      }
      
      .coupon-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
    
    .coupon-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.2;
    }
  }
  
  .amount-placeholder {
    font-size: 28rpx;
    color: #999;
  }
}

.confirm-btn {
  width: 200rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #3c69dc 0%, #5a7ce8 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(60, 105, 220, 0.3);
  
  &.disabled {
    background: #f5f5f5;
    box-shadow: none;
    
    .btn-text {
      color: #999;
    }
  }
  
  &:not(.disabled):active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(60, 105, 220, 0.4);
  }
}

.btn-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
}

.loading,
.no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}
</style>