<script setup lang="ts">
// @ts-nocheck
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import { useMemberStore } from '@/stores'
import { getAvailableCouponsAPI, claimCouponAPI } from '@/services/coupon'
import type { CouponItem as OriginalCouponItem, CouponType } from '@/types/coupon'

// 扩展CouponItem类型，添加前端需要的属性
interface CouponItem extends OriginalCouponItem {
  claimed?: boolean;
  startTime: number; // 覆盖string类型为number
  endTime: number;   // 覆盖string类型为number
}
import CouponItemComponent from "@/components/CouponItem.vue"

// 安全区
const { safeAreaInsets } = uni.getSystemInfoSync()
const bottomInset = safeAreaInsets?.bottom || 0

// 获取会员信息
const memberStore = useMemberStore()

// 数据与状态
const loading = ref(true)
const couponList = ref<CouponItem[]>([])
const filterType = ref<0 | CouponType>(0) // 0=全部
const sortBy = ref<"default" | "ending" | "value">("default")

// 获取可领取的优惠券列表
const getAvailableCoupons = async () => {
  try {
    loading.value = true
    const params = {
      sortBy: sortBy.value,
      pageNum: 1,
      pageSize: 100 // 获取更多数据用于前端筛选排序
    }
    
    // 只有当筛选类型不是"全部"时，才添加couponType参数
    if (filterType.value !== 0) {
      params.couponType = filterType.value
    }
    
    // 添加用户ID参数，用于过滤已领取的优惠券
    const userId = memberStore.profile.user?.id
    if (userId) {
      params.userId = userId
    }
    
    const result = await getAvailableCouponsAPI(params)
    if (result.code === 200) {
      // 转换时间格式
      couponList.value = result.rows.map(item => ({
        ...item,
        startTime: new Date(item.startTime).getTime(),
        endTime: new Date(item.endTime).getTime(),
        claimed: false // 初始状态为未领取
      }))
    } else {
      uni.showToast({ title: result.msg || "获取优惠券失败", icon: "error" })
    }
  } catch (e) {
    console.error("获取优惠券列表失败:", e)
    uni.showToast({ title: "获取优惠券失败", icon: "error" })
  } finally {
    loading.value = false
  }
}

// 计算属性：筛选+排序后的列表
const filteredSortedCoupons = computed(() => {
  const now = Date.now()
  let list = couponList.value.filter((c) => now <= c.endTime && c.remainCount > 0)
  if (filterType.value !== 0) {
    list = list.filter((c) => c.couponType === filterType.value)
  }
  switch (sortBy.value) {
    case "ending":
      list = list.slice().sort((a, b) => a.endTime - b.endTime)
      break
    case "value":
      list = list
        .slice()
        .sort((a, b) => getCouponValue(b) - getCouponValue(a))
      break
    default:
      // 默认按是否快过期(7天内)优先，其次按剩余量
      const soon = (c: CouponItem) => (c.endTime - now) <= (7 * 24 * 3600 * 1000)
      list = list
        .slice()
        .sort((a, b) => Number(soon(b)) - Number(soon(a)) || b.remainCount - a.remainCount)
  }
  return list
})

// 估算券“价值”用于排序：满减/无门槛取 faceValue，折扣按(10-折扣)*10 近似，包邮按固定权重
function getCouponValue(c: CouponItem) {
  if (c.couponType === 1 || c.couponType === 3) return c.faceValue || 0
  if (c.couponType === 2) return Math.round(((10 - (c.discount || 10)) * 10))
  if (c.couponType === 4) return 30
  return 0
}


// 领取逻辑
function isCouponClaimable(coupon: CouponItem) {
  const now = Date.now()
  return coupon.remainCount > 0 && now <= coupon.endTime && coupon.claimed !== true
}

async function claimCoupon(id: number) {
  const item = couponList.value.find((c) => c.id === id)
  if (!item) return
  
  // 检查是否可领取
  if (!isCouponClaimable(item)) {
    if (item.claimed) {
      uni.showToast({ title: "您已领取过此优惠券", icon: "none" })
    } else if (item.remainCount <= 0) {
      uni.showToast({ title: "优惠券已被抢光", icon: "none" })
    } else if (new Date().getTime() > item.endTime) {
      uni.showToast({ title: "优惠券已过期", icon: "none" })
    } else {
      uni.showToast({ title: "暂不可领取", icon: "none" })
    }
    return
  }

  try {
    const userId = memberStore.profile?.user.id
    if (!userId) {
      uni.showToast({ title: "请先登录后再领取", icon: "none", duration: 2000 })
      // 可以在这里添加跳转到登录页的逻辑
      setTimeout(() => {
        uni.navigateTo({ url: '/pages/login/login' })
      }, 1500)
      return
    }

    // 显示加载中
    uni.showLoading({ title: "领取中..." })
    
    const result = await claimCouponAPI({ userId, couponId: id })
    
    // 隐藏加载提示
    uni.hideLoading()
    
    if (result.code === 200) {
      // 显示成功动画和提示
      uni.showToast({ 
        title: "领取成功，可在'我的优惠券'中查看", 
        icon: "success",
        duration: 2000
      })
      
      // 重新请求数据刷新列表
      await getAvailableCoupons()
    } else {
      // 显示具体的失败原因
      uni.showToast({ 
        title: result.msg || "领取失败，请稍后再试", 
        icon: "error",
        duration: 2000
      })
    }
  } catch (e) {
    uni.hideLoading()
    console.error("领取优惠券失败:", e)
    uni.showToast({ 
      title: "网络异常，请检查网络连接", 
      icon: "error",
      duration: 2000
    })
  }
}

async function claimAllVisible() {
  const list = filteredSortedCoupons.value.filter((c) => isCouponClaimable(c))
  if (list.length === 0) {
    uni.showToast({ title: "暂无可领取", icon: "none" })
    return
  }

  const userId = memberStore.profile?.user.id
  if (!userId) {
    uni.showToast({ title: "请先登录", icon: "none" })
    return
  }

  let successCount = 0
  for (const coupon of list) {
    try {
      const result = await claimCouponAPI({ userId, couponId: coupon.id })
      if (result.code === 200) {
        coupon.claimed = true
        coupon.remainCount = Math.max(0, coupon.remainCount - 1)
        successCount++
      }
    } catch (e) {
      console.error("领取优惠券失败:", e)
    }
  }
  
  if (successCount > 0) {
    uni.showToast({ title: `成功领取 ${successCount} 张`, icon: "success" })
  } else {
    uni.showToast({ title: "领取失败", icon: "error" })
  }
}

// 处理优惠券领取事件
const handleClaim = (coupon: any) => {
  console.log('领取优惠券:', coupon.id)
  claimCoupon(coupon.id)
}


// 生命周期
onLoad(async () => {
  // 初次进入：先展示骨架屏，再加载本地数据
  await getAvailableCoupons()
})
</script>

<template>
  <view class="viewport" :style="{ paddingBottom: bottomInset + 'px' }">
    <!-- 宣传横幅 -->
    <view class="banner">
      <image 
        src="/static/images/coupon-banner.png" 
        mode="aspectFill"
        class="banner-image"
      />
      <view class="banner-content">
        <text class="banner-title">现金红包限时领</text>
        <text class="banner-subtitle">最高可领188元</text>
      </view>
    </view>

    <!-- 筛选与操作栏（吸顶） -->
    <view class="filters">
      <scroll-view class="tabs" scroll-x enable-flex>
        <view
          class="tab"
          :class="{ active: filterType === 0 }"
          @tap="filterType = 0"
        >
          全部
        </view>
        <view
          class="tab"
          :class="{ active: filterType === 1 }"
          @tap="filterType = 1 as any"
        >
          满减
        </view>
        <view
          class="tab"
          :class="{ active: filterType === 2 }"
          @tap="filterType = 2 as any"
        >
          折扣
        </view>
        <view
          class="tab"
          :class="{ active: filterType === 3 }"
          @tap="filterType = 3 as any"
        >
          无门槛
        </view>
        <view
          class="tab"
          :class="{ active: filterType === 4 }"
          @tap="filterType = 4 as any"
        >
          包邮
        </view>
      </scroll-view>

      <view class="sort-row">
        <view
          class="sort-chip"
          :class="{ active: sortBy === 'default' }"
          @tap="sortBy = 'default'"
        >
          默认
        </view>
        <view
          class="sort-chip"
          :class="{ active: sortBy === 'ending' }"
          @tap="sortBy = 'ending'"
        >
          快过期
        </view>
        <view
          class="sort-chip"
          :class="{ active: sortBy === 'value' }"
          @tap="sortBy = 'value'"
        >
          面值最大
        </view>

        <view class="spacer"></view>

        <view class="claim-all" @tap="claimAllVisible">一键领取</view>
      </view>
    </view>

    <!-- 列表 -->
    <scroll-view class="coupon-list" scroll-y>
      <!-- 骨架屏 -->
      <view v-if="loading" class="skeleton-list">
        <view class="skeleton-item" v-for="i in 4" :key="i">
          <view class="skeleton-left"></view>
          <view class="skeleton-right"></view>
        </view>
      </view>

      <!-- 空态 -->
      <view v-else-if="filteredSortedCoupons.length === 0" class="empty">
        <image src="/static/images/empty-coupon.png" mode="aspectFit" />
        <text>暂无可领取的优惠券</text>
      </view>

      <!-- 列表项 -->
      <view v-else class="coupon-items">
      <view 
        v-for="coupon in filteredSortedCoupons" 
        :key="coupon.id"
        class="coupon-wrapper"
        @tap="handleClaim(coupon)"
      >
        <CouponItemComponent
          :coupon="coupon"
          mode="claim"
        />
      </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss">
.viewport {
  min-height: 100vh;
  background-color: #f7f7f8;
  display: flex;
  flex-direction: column;
  position: relative;
}

.banner {
  position: relative;
  height: 200rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  
  .banner-image {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .banner-content {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    
    .banner-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
      margin-bottom: 8rpx;
    }
    
    .banner-subtitle {
      font-size: 24rpx;
      color: #fff;
      opacity: 0.9;
    }
  }
}
.filters {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #f7f7f8;
  padding: 16rpx 20rpx 8rpx;
  border-bottom: 1rpx solid #eee;
  .tabs {
    white-space: nowrap;
    display: flex;
    gap: 16rpx;
    padding-bottom: 8rpx;
    .tab {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 12rpx 20rpx;
      font-size: 24rpx;
      color: #666;
      background: #ffffff;
      border: 1rpx solid #eee;
      border-radius: 999rpx;
      &.active {
        color: #ef4444;
        border-color: #ef4444;
        background: #fff5f5;
        font-weight: 600;
      }
    }
  }
  .sort-row {
    margin-top: 6rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    .sort-chip {
      padding: 10rpx 18rpx;
      font-size: 24rpx;
      color: #666;
      background: #ffffff;
      border: 1rpx solid #eee;
      border-radius: 999rpx;
      &.active {
        color: #ef4444;
        border-color: #ef4444;
        background: #fff5f5;
        font-weight: 600;
      }
    }
    .spacer {
      flex: 1;
    }
    .claim-all {
      padding: 12rpx 24rpx;
      background: #ef4444;
      color: #fff;
      border-radius: 999rpx;
      font-size: 24rpx;
      font-weight: 600;
    }
  }
}
.coupon-list {
  flex: 1;
  padding: 20rpx;
  height: 0;
}
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }
  text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 骨架屏 */
.skeleton-list {
  display: grid;
  gap: 20rpx;
}
.skeleton-item {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  overflow: hidden;
  .skeleton-left {
    flex: 1;
    height: 220rpx;
    border-radius: 12rpx;
    background: linear-gradient(90deg, #f1f1f1, #f7f7f7, #f1f1f1);
    background-size: 200% 100%;
    animation: shimmer 1.2s infinite;
  }
  .skeleton-right {
    width: 200rpx;
    margin-left: 30rpx;
    border-radius: 25rpx;
    background: linear-gradient(90deg, #f1f1f1, #f7f7f7, #f1f1f1);
    background-size: 200% 100%;
    animation: shimmer 1.2s infinite;
  }
}
@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    max-height: 200rpx;
    transform: translateY(0);
  }
}

.coupon-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.coupon-wrapper {
  position: relative;
  cursor: pointer;
}
</style>
