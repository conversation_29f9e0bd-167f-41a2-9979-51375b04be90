<script setup lang="ts">
import { getOssParam, putMemberProfileAPI } from '@/services/member'
import { useMemberStore } from '@/stores'
import type { LoginResult, OSSItem } from '@/types/member'
import { ref } from 'vue'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()
const memberStore = useMemberStore()
// 创建一个用户信息的响应式引用
const profile = ref(memberStore.profile?.user || {})

const onAvatarChange = () => {
  uni.chooseMedia({
    count: 1,
    mediaType: ['image'],
    success: async (res) => {
      const { tempFilePath } = res.tempFiles[0]
      const memberStore = useMemberStore()
      // 修改上传接口地址
      uni.uploadFile({
        url: 'http://localhost:18080/file/upload', // 使用完整的URL地址
        name: 'file',
        filePath: tempFilePath,
        header: {
          Authorization: memberStore.profile?.token,
        },
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data)
            if (data.code === 200) {
              const avatar = data.data.url
              // 当前页面更新头像
              profile.value.avatar = avatar
              // 更新 Store 头像
              if (memberStore.profile?.user) {
                memberStore.profile.user.avatar = avatar
              }
              // 调用更新用户信息接口
              putMemberProfileAPI({
                ...profile.value,
                avatar: avatar,
              }).then((res) => {
                if (res.code === 200) {
                  uni.showToast({ icon: 'success', title: '头像更新成功' })
                } else {
                  uni.showToast({ icon: 'error', title: res.msg || '头像更新失败' })
                }
              })
            } else {
              uni.showToast({ icon: 'error', title: data.msg || '头像上传失败' })
            }
          } catch (error) {
            console.error('解析上传响应失败', error)
            uni.showToast({ icon: 'error', title: '头像上传失败' })
          }
        },
        fail: (error) => {
          uni.showToast({ icon: 'error', title: '头像上传失败' })
          console.error('会员头像上传失败', error)
        },
      })
    },
  })
}

// 修改性别
const onGenderChange: UniHelper.RadioGroupOnChange = (ev) => {
  profile.value.gender = Number(ev.detail.value)
}

// 修改生日
const onBirthdayChange: UniHelper.DatePickerOnChange = (ev) => {
  profile.value.birthday = ev.detail.value
}

// 修改城市
const onFullLocationChange: UniHelper.RegionPickerOnChange = (ev) => {
  profile.value.fullLocation = ev.detail.value.join('')
}

// 提交修改
const onSubmit = async () => {
  const res = await putMemberProfileAPI(profile.value)
  if (res.code === 200) {
    // 更新 Store 中的用户信息
    if (memberStore.profile?.user) {
      memberStore.profile.user = {
        ...memberStore.profile.user,
        ...res.result,
      }
    }
    uni.showToast({ icon: 'success', title: res.msg })
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  } else {
    uni.showToast({ icon: 'error', title: res.msg })
  }
}
</script>

<template>
  <view class="viewport">
    <!-- 导航栏 -->
    <view class="navbar" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
      <navigator open-type="navigateBack" class="back icon-left" hover-class="none"></navigator>
      <view class="title">个人信息</view>
    </view>
    <!-- 头像 -->
    <view class="avatar">
      <view class="avatar-content" @tap="onAvatarChange">
        <img
          class="image"
          :src="
            profile.avatar ||
            'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
          "
          mode="aspectFill"
        />
        <text class="text">点击修改头像</text>
      </view>
    </view>
    <!-- 表单 -->
    <view class="form">
      <view class="form-content">
        <view class="form-item">
          <text class="label">账号</text>
          <input class="input" type="text" placeholder="请填写账号" v-model="profile.username" />
        </view>
        <view class="form-item">
          <text class="label">昵称</text>
          <input class="input" type="text" placeholder="请填写昵称" v-model="profile.nickname" />
        </view>
        <view class="form-item">
          <text class="label">手机</text>
          <input
            class="input"
            type="text"
            placeholder="请填写手机号"
            :value="profile.mobile"
            disabled
          />
        </view>
        <view class="form-item">
          <text class="label">性别</text>
          <radio-group @change="onGenderChange">
            <label class="radio">
              <radio value="1" color="#3c69dc" :checked="profile.gender === 1" />
              男
            </label>
            <label class="radio">
              <radio value="2" color="#3c69dc" :checked="profile.gender === 2" />
              女
            </label>
          </radio-group>
        </view>
        <view class="form-item">
          <text class="label">生日</text>
          <picker
            class="picker"
            mode="date"
            start="1900-01-01"
            :end="new Date()"
            :value="profile.birthday"
            @change="onBirthdayChange"
          >
            <view v-if="profile.birthday">{{ profile.birthday }}</view>
            <view class="placeholder" v-else>请选择日期</view>
          </picker>
        </view>
        <view class="form-item">
          <text class="label">城市</text>
          <picker
            class="picker"
            mode="region"
            :value="profile.fullLocation?.split(' ')"
            @change="onFullLocationChange"
          >
            <view v-if="profile.fullLocation">{{ profile.fullLocation }}</view>
            <view class="placeholder" v-else>请选择城市</view>
          </picker>
        </view>
        <view class="form-item">
          <text class="label">职业</text>
          <input class="input" type="text" placeholder="请填写职业" v-model="profile.profession" />
        </view>
      </view>
      <!-- 提交按钮 -->
      <button class="form-button" @tap="onSubmit">保 存</button>
    </view>
  </view>
</template>

<style lang="scss">
page {
  background-color: #f4f4f4;
}

.viewport {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #3c69dc;
  background-size: auto 420rpx;
  background-repeat: no-repeat;
}

// 导航栏
.navbar {
  position: relative;

  .title {
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
  }

  .back {
    position: absolute;
    height: 40px;
    width: 40px;
    left: 0;
    font-size: 20px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 头像
.avatar {
  text-align: center;
  width: 100%;
  height: 260rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    background-color: #eee;
  }

  .text {
    display: block;
    padding-top: 20rpx;
    line-height: 1;
    font-size: 26rpx;
    color: #fff;
  }
}

// 表单
.form {
  background-color: #f4f4f4;

  &-content {
    margin: 20rpx 20rpx 0;
    padding: 0 20rpx;
    border-radius: 10rpx;
    background-color: #fff;
  }

  &-item {
    display: flex;
    height: 96rpx;
    line-height: 46rpx;
    padding: 25rpx 10rpx;
    background-color: #fff;
    font-size: 28rpx;
    border-bottom: 1rpx solid #ddd;

    &:last-child {
      border: none;
    }

    .label {
      width: 180rpx;
      color: #333;
    }

    .account {
      color: #666;
    }

    .input {
      flex: 1;
      display: block;
      height: 46rpx;
    }

    .radio {
      margin-right: 20rpx;
    }

    .picker {
      flex: 1;
    }
    .placeholder {
      color: #808080;
    }
  }

  &-button {
    height: 80rpx;
    text-align: center;
    line-height: 80rpx;
    margin: 30rpx 20rpx;
    color: #fff;
    border-radius: 80rpx;
    font-size: 30rpx;
    background-color: #3c69dc;
  }
}
</style>
