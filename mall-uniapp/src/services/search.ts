import { http } from '@/utils/http'

// 搜索参数接口
export interface SearchParams {
  keyword: string
  page: number
  size: number
  brandId?: number
  brandName?: string
  categoryId?: number
  categoryName?: string
  minPrice?: number
  maxPrice?: number
}

// 搜索建议参数接口
export interface SuggestionParams {
  prefix: string
}

// 商品项接口
export interface GoodsItem {
  id: string | null
  goodsId: number
  goodsName: string
  goodsImg: string
  goodsPrice: number
  brandId: number
  brandName: string
  categoryId: number
  categoryName: string
  status: number
  suggestion: string[]
}

// 聚合数据接口
export interface AggregationResult {
  品牌: string[]
  分类: string[]
  价格区间: string[]
}

// 价格区间接口
export interface PriceRange {
  min: number
  max: number
  text: string
  count: number
}

// 搜索结果接口
export interface SearchResult {
  total: number
  goodsDtoList: GoodsItem[]
}

// 搜索商品
export const searchGoods = (params: SearchParams) => {
  return http<SearchResult>({
    url: '/search/goods/search',
    method: 'POST',
    data: params,
  })
}

// 获取搜索建议
export const getSuggestions = (prefix: string) => {
  return http<string[]>({
    url: '/search/goods/suggestion',
    method: 'GET',
    data: { prefix },
  })
}

// 获取聚合信息
export const getAggregations = (params: SearchParams) => {
  return http<SearchResult>({
    url: '/search/goods/aggregate',
    method: 'GET',
    data: params,
  })
}
