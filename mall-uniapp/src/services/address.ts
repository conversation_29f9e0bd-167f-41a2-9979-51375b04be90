import type { AddressParams } from '@/types/address'
import { http } from '@/utils/http'

/**
 * 添加收货地址
 * @param data 请求参数
 */
export const postMemberAddressAPI = (address: AddressParams, opr = 'insert') => {
  return http({
    method: 'POST',
    url: '/member/user/address',
    data: { address: address, opr: opr },
  })
}

/**
 * 获取我的收货地址列表,id为登录用户的id
 */
export const getMemberAddressAPI = (id: number) => {
  return http<AddressParams[]>({
    method: 'POST',
    url: '/member/user/address',
    data: { userId: id, opr: 'findByUserId' },
  })
}

/**
 * 通过地址id获取收货地址详情
 * @param id 地址id
 */
export const getMemberAddressByIdAPI = (id: number) => {
  return http<AddressParams>({
    method: 'POST',
    url: '/member/user/address',
    data: { id: id, opr: 'findById' },
  })
}

/**
 * 修改收货地址
 * @param id 地址id(路径参数)
 * @param data 表单数据(请求体参数)
 */
export const putMemberAddressByIdAPI = (address: AddressParams, opr = 'update') => {
  return http({
    method: 'POST',
    url: '/member/user/address',
    data: { address: address, opr: opr },
  })
}

/**
 * 删除收货地址
 * @param id 地址id(路径参数)
 */
export const deleteMemberAddressByIdAPI = (id: number, opr = 'del') => {
  return http({
    method: 'POST',
    url: '/member/user/address',
    data: { id: id, opr: opr },
  })
}

/**
 * 获取登录用户默认的收货地址
 * @param userId 登录用户的id
 */
export const getDefaultAddressByUserIdAPI = (userId: number, opr = 'getDefaultAdd') => {
  return http<AddressParams>({
    method: 'POST',
    url: '/member/user/address',
    data: { userId: userId, opr: opr },
  })
}
