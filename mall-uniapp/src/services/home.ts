import type { PageResult } from '@/types/global'
import type { BannerItem, CategoryItem, GuessItem } from '@/types/home'
// 从src/utils/http.ts中导入http事件。
import { http } from '@/utils/http'

export const getHomeBannerAPI = (type = 1) => {
  return http<BannerItem[]>({
    // ctrl + i 可以进行手动导入
    url: '/goods/home/<USER>', // 完整地址为：http://localhost:8081/uni/home/<USER>
    method: 'GET',
    data: {
      // 广告区域展示位置，1为首页。 2为分类商品页，默认值为1
      type,
      opr: 'list',
    },
  })
}

// services/home.ts
/**
 * 首页-前台分类-小程序
 */
export const getHomeCategoryAPI = () => {
  return http<CategoryItem[]>({
    method: 'GET',
    url: '/goods/home/<USER>/mutli',
    data: { opr: 'list' },
  })
}

/**
 * 猜你喜欢-小程序
 * 参数currPageNo为当前页码。 pageSize为每页显示的数据行数。
 */
export const getHomeGoodsGuessLikeAPI = (currPageNo = 1, pageSize = 6) => {
  return http<PageResult<GuessItem>>({
    // 传入泛型，GuessItem是商品类型
    method: 'GET',
    url: '/goods/home/<USER>',
    data: { currPageNo: currPageNo, pageSize: pageSize, opr: 'list' },
  })
}
