import type { PayParams, WechatRefundParams } from '@/types/pay'
import { http } from '@/utils/http'

/**
 * 获取微信支付参数
 * @param data orderId 订单id，userId为用户id
 */
export const getWxPayAPI = (data: { opr: string; userId: number; orderId: number }) => {
  return http<PayParams>({
    method: 'GET',
    url: '/pay',
    data,
  })
}

/**
 * 查询微信支付状态
 * @param orderSn 订单号
 */
export const queryPayStatusAPI = (orderSn: string) => {
  return http<boolean>({
    method: 'GET',
    url: `/pay/query/${orderSn}`,
  })
}

export const getWxWechatRefundAPI = (data: WechatRefundParams) => {
  return http<WechatRefundParams>({
    method: 'POST',
    url: '/order/information/wechat/refund',
    data: data.data, // 直接发送 data 对象内的数据
  })
}
