import type { GoodsItem } from '@/types/goods'
import { http } from '@/utils/http'
// 通过商品id查询商品详情信息
export const getGoodsByIdAPI = (id: number) => {
  return http<GoodsItem>({
    method: 'GET',
    url: '/goods',
    data: { id, opr: 'findById' },
  })
}

/**
 * 商品搜索参数类型
 */
type GoodsSearchParams = {
  /** 搜索关键词 */
  keyword: string
  /** 页码 */
  page: number
  /** 每页条数 */
  pageSize: number
  /** 排序字段 */
  sortField?: string
  /** 排序方式 */
  sortOrder?: string
}

/**
 * 商品搜索
 * @param data 请求参数
 */
export const getGoodsSearchAPI = (data: GoodsSearchParams) => {
  return http<any>({
    method: 'GET',
    url: '/goods/search',
    data,
  })
}
