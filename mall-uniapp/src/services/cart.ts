import type { CartItem } from '@/types/cart'
import { http } from '@/utils/http'
// 请求后端实现加入购物车的功能， skuId为商品的skuId，goodsCount为加入购物车的数量，
// userId为会员id
export const postMemberCartAPI = (
  cart: { userId: number; skuId: number; goodsCount: number },
  opr = 'addCart',
) => {
  return http({
    method: 'POST',
    url: '/member/user/cart',
    data: { opr: opr, cart: cart },
  })
}

/**
 * 获取当前登录用户购物车中的商品列表
 */
export const getMemberCartAPI = (userId: number, opr = 'findCartGoodsList') => {
  return http<CartItem[]>({
    method: 'POST',
    url: '/member/user/cart',
    data: { userId: userId, opr: opr },
  })
}

/**
 * 删除/清空购物车单品
 * @param data 请求体参数 ids SKUID 集合
 */
export const deleteMemberCartAPI = (ids: number[], opr = 'delCart') => {
  return http({
    method: 'POST',
    url: '/member/user/cart',
    data: { ids: ids, opr: opr },
  })
}
/**
 * 修改购物车单品
 * @param id 购物车id
 * @param data selected 选中状态 count 商品数量
 */
export const putMemberCartBySkuIdAPI = (data: {
  id: number
  opr?: string
  selected?: boolean
  count?: number
}) => {
  return http({
    method: 'POST',
    url: '/member/user/cart',
    data,
  })
}
/**
 * 修改购物车列表中全选/全不选
 * @param data userId为登录用户id， opr为操作符
 */
export const putMemberCartSelectedAPI = (data: {
  userId: number
  opr: string
  selected: boolean
}) => {
  return http({
    method: 'POST',
    url: '/member/user/cart',
    data,
  })
}
