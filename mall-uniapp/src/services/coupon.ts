import { http } from '@/utils/http'

// 优惠券类型定义
export interface CouponItem {
  id: number
  name: string
  description: string
  discount: number
  minAmount: number
  maxDiscount?: number
  type: 'fixed' | 'percent' // fixed: 固定金额, percent: 百分比
  status: 'available' | 'used' | 'expired'
  startTime: string
  endTime: string
}

// 用户优惠券类型定义
export interface UserCouponItem {
  id: number
  name: string
  description: string
  discount: number
  minAmount: number
  maxDiscount?: number
  type: 'fixed' | 'percent'
  status: 'available' | 'used' | 'expired'
  startTime: string
  endTime: string
  receiveTime: string
  useTime?: string
}

// 用户优惠券状态类型
export type UserCouponStatus = 'available' | 'used' | 'expired'

// 优惠券列表查询参数
export interface CouponListDTO {
  pageNum?: number
  pageSize?: number
  couponName?: string
  couponType?: number
}

// 用户优惠券列表查询参数
export interface UserCouponListDTO {
  pageNum?: number
  pageSize?: number
  userId: number
  useStatus?: number // 0-未使用, 1-已使用, 2-已过期
}

// 领取优惠券参数
export interface CouponClaimDTO {
  userId: number
  couponId: number
}

// 获取用户可用优惠券列表（用于订单页面）
export const getMemberCouponsAPI = (userId: number, orderAmount: number) => {
  return http<UserCouponItem[]>({
    method: 'GET',
    url: `/coupon/my`,
    data: {
      userId,
      useStatus: 0, // 只获取未使用的优惠券
      pageNum: 1,
      pageSize: 100
    }
  })
}

// 获取用户优惠券列表
export const getUserCouponListAPI = (params: UserCouponListDTO) => {
  return http({
    method: 'GET',
    url: `/coupon/my`,
    data: params
  })
}

// 获取可领取的优惠券列表
export const getAvailableCouponsAPI = (params?: CouponListDTO) => {
  return http({
    method: 'GET',
    url: `/coupon/available`,
    data: params || {}
  })
}

// 领取优惠券
export const claimCouponAPI = (params: CouponClaimDTO) => {
  return http({
    method: 'POST',
    url: `/coupon/claim`,
    data: params
  })
}

// 检查用户是否已领取指定优惠券
export const checkClaimedAPI = (userId: number, couponId: number) => {
  return http({
    method: 'GET',
    url: `/coupon/check/${userId}/${couponId}`
  })
}

// 使用优惠券
export const useCouponAPI = (couponId: number, orderId: number) => {
  return http({
    method: 'POST',
    url: `/coupon/use`,
    data: {
      couponId,
      orderId
    }
  })
}
