import { http } from '@/utils/http'

// 定义接口
export interface Activity {
  id: number
  activityName: string
  activityDesc: string
  startTime: string
  activityImage?: string
  endTime: string
  status: number
  enabled: number
  createTime?: string
  updateTime?: string
  createBy?: string
  updateBy?: string
  remark?: string
}

export interface ActivityQuery {
  pageNum?: number
  pageSize?: number
  activityName?: string
  status?: number
  enabled?: number
}

// 查询秒杀活动列表
export function listActivity(query: ActivityQuery) {
  return http<any>({
    url: '/ktmall/seckill/activity/list',
    method: 'GET',
    data: query,
  })
}

// 查询全部的秒杀列表
export function getListActivity() {
  return http<any>({
    url: '/seckill/activity/getList',
    method: 'GET',
  })
}

// 查询秒杀活动详细
export function getActivity(id: number) {
  return http<Activity>({
    url: '/ktmall/seckill/activity/' + id,
    method: 'GET',
  })
}

// 新增秒杀活动
export function addActivity(data: Activity) {
  return http({
    url: '/ktmall/seckill/activity',
    method: 'POST',
    data: data,
  })
}

// 修改秒杀活动
export function updateActivity(data: Activity) {
  return http({
    url: '/ktmall/seckill/activity',
    method: 'PUT',
    data: data,
  })
}

// 删除秒杀活动
export function delActivity(id: number) {
  return http({
    url: '/ktmall/seckill/activity/' + id,
    method: 'DELETE',
  })
}
