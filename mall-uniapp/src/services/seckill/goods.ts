import type { GoodsItem } from '@/types/goods'
import { http } from '@/utils/http'

// 定义接口
export interface SeckillGoods {
  id: number
  activityId: number
  goodsId: number
  skuId: number
  seckillPrice: number
  seckillStock: number
  stockLocked: number
  status: number
  enabled: number
  createTime?: string
  updateTime?: string
  remark?: string
  // 关联字段
  activityName?: string
  goodsName?: string
  stockSku?: string
  // 商品图片
  goodsImg?: string
}

export interface SeckillGoodsQuery {
  pageNum?: number
  pageSize?: number
  activityId?: number
  goodsId?: number
  skuId?: number
  status?: number
  enabled?: number
}

// 查询秒杀商品列表
export function getSeckillGoodsList(page: number, size: number, activityId?: number) {
  return http<SeckillGoods[]>({
    url: '/seckill/goods/getSeckillGoodsList',
    method: 'GET',
    data: { page, size, activityId },
  })
}

// 查询秒杀商品详情
export function getSeckillGoodsDetail(goodsId: number) {
  return http<GoodsItem[]>({
    url: '/seckill/goods/getSeckillGoodsDetail',
    method: 'GET',
    data: { goodsId },
  })
}
