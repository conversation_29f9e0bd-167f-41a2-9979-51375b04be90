import { http } from '@/utils/http'

// 定义接口
export interface SeckillOrder {
  id: number
  orderSn: string
  userId: number
  activityId: number
  seckillGoodsId: number
  skuId: number
  buyCount: number
  seckillPrice: number
  totalAmount: number
  status: number
  payTime?: string
  enabled: number
  createTime?: string
  updateTime?: string
  remark?: string
  // 关联字段
  activityName?: string
  goodsName?: string
  stockSku?: string
}

export interface SeckillOrderQuery {
  pageNum?: number
  pageSize?: number
  orderSn?: string
  userId?: number
  activityId?: number
  seckillGoodsId?: number
  skuId?: number
  status?: number
  payTime?: string
  enabled?: number
}

// 秒杀订单数据接口
export interface SeckillOrderData {
  userId: number
  activityId: number
  goodsId: number
  secKillGoodsId: number
  skuId: number
  buyCount: number
  secKillPrice: number
  totalAmount: number
  addressId: number
  attrsText: string
}

// 查询秒杀订单列表
export function listOrder(query: SeckillOrderQuery) {
  return http<SeckillOrder[]>({
    url: '/ktmall/seckill/order/list',
    method: 'GET',
    data: query,
  })
}

// 查询秒杀订单详细
export function getOrder(id: number) {
  return http<SeckillOrder>({
    url: '/ktmall/seckill/order/' + id,
    method: 'GET',
  })
}

// 新增秒杀订单
export function addOrder(data: SeckillOrder) {
  return http({
    url: '/ktmall/seckill/order',
    method: 'POST',
    data: data,
  })
}

// 修改秒杀订单
export function updateOrder(data: SeckillOrder) {
  return http({
    url: '/ktmall/seckill/order',
    method: 'PUT',
    data: data,
  })
}

// 删除秒杀订单
export function delOrder(id: number) {
  return http({
    url: '/ktmall/seckill/order/' + id,
    method: 'DELETE',
  })
}

// 生成秒杀地址
export function generateSeckillUrl(data: SeckillOrderData) {
  return http({
    url: '/seckill/user/order/generateUrl',
    method: 'POST',
    data: data,
  })
}

// 创建秒杀订单（使用动态地址）
export function createSeckillOrder(data: SeckillOrderData, seckillUrl: string) {
  return http({
    url: `/seckill/user/order/${seckillUrl}`,
    method: 'POST',
    data: data,
  })
}

// 秒杀订单支付
export function seckillOrderPrepay(data: { userId: number; orderId: number }) {
  return http({
    url: '/seckill/user/order/pay/prepay',
    method: 'GET',
    data: data,
  })
}
