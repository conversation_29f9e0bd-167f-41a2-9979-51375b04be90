// 用于从小程序向后端发送会员信息相关的请求，比如上传头像、修改会员信息等。
// 从src/utils/http.ts中导入http事件。
import type { LoginResult, OSSItem } from '@/types/member'
import { http } from '@/utils/http'

// 请求后端，用于获取OSS服务端签名数据。
export const getOssParam = () => {
  return http<OSSItem>({
    method: 'GET',
    url: '/oss/policy',
  })
}

/**
 * 修改会员信息
 * @param wxUser 为要修改的会员信息
 * @param opr 为操作符，update表示本次操作为修改会员信息
 */
export const putMemberProfileAPI = (wxUser: LoginResult, opr = 'update') => {
  return http<LoginResult>({
    method: 'POST',
    url: '/member/wxUser/profile',
    data: { wxUser: wxUser, opr: opr },
  })
}
