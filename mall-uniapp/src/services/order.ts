import type {
  OrderCreateParams,
  OrderListParams,
  OrderListResult,
  OrderResult,
} from '@/types/order'
import { http } from '@/utils/http'

/**
 * 提交订单
 * @param data 请求参数
 */
export const postMemberOrderAPI = (order: OrderCreateParams) => {
  return http<{ id: number }>({
    method: 'POST',
    url: '/order/member',
    data: { opr: 'addOrder', order: order },
  })
}

/**
 * 再次购买
 * @param id 订单id
 */
export const orderAgainPurchase = (data: { opr: string; orderId: number; userId: number }) => {
  return http<OrderResult>({
    method: 'POST',
    url: '/order/member',
    data,
  })
}

/**
 * 获取订单详情
 * @param id 订单id
 */
export const getMemberOrderByIdAPI = (data: { opr: string; orderId: number }) => {
  return http<OrderResult>({
    method: 'POST',
    url: '/order/member',
    data,
  })
}

/**
 * 删除订单
 * @param id 订单id
 */
export const deleteByIdMemberOrderAPI = (data: { opr: string; orderId: number }) => {
  return http<OrderResult>({
    method: 'POST',
    url: '/order/member',
    data,
  })
}

/**
 * 取消订单
 */
export const cancelMemberOrderAPI = (data: { opr: string; orderId: number }) => {
  return http<OrderResult>({
    method: 'POST',
    url: '/order/member',
    data,
  })
}

/**
 * 获取订单列表
 */
export const getMemberOrderAPI = (orderParam: OrderListParams) => {
  return http<OrderListResult>({
    method: 'POST',
    url: `/order/member`,
    data: { orderParam: orderParam, opr: 'pageOrderList' },
  })
}
