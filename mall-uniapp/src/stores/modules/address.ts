import type { AddressParams } from '@/types/address'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAddressStore = defineStore('address', () => {
  // selectedAddress用于保存登录用户的默认收货地址
  const selectedAddress = ref<AddressParams>()

  // 定义用于修改登录用户的默认收货地址的事件
  const changeSelectedAddress = (val: AddressParams) => {
    selectedAddress.value = val
  }

  return {
    selectedAddress,
    changeSelectedAddress,
  }
})
