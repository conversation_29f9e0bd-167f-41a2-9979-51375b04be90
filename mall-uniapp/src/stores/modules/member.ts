// modules是模块，即当前这个member会员模块
import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义 Store
export const useMemberStore = defineStore(
  'member',
  () => {
    // 用于保存登录后的会员信息(会员对象)
    const profile = ref<any>() // 定义属性，属性名为profile，类似于Vuex中的state中定义的属性，另外any表示此属性的类型为任意类型。

    // 保存会员信息，登录时使用  (类似于set方法，用于赋值)
    const setProfile = (val: any) => {
      // val为参数，类型为any即任意类型。
      profile.value = val
    }

    // 清理会员信息，退出时使用，将profile会员对象中的内容清空。
    const clearProfile = () => {
      profile.value = undefined
    }

    // 记得 return
    return {
      profile,
      setProfile,
      clearProfile,
    }
  },
  // TODO: 持久化
  {
    persist: {
      storage: {
        getItem(key) {
          // 从storage中获取key所对应的值
          return uni.getStorageSync(key)
        },
        setItem(key, value) {
          //将value值保存到storage中
          uni.setStorageSync(key, value)
        },
      },
    },
  },
)
