<script setup lang="ts">
import type { BannerItem } from '@/types/home'
import { ref } from 'vue'

const activeIndex = ref(0)
// 轮播图片改变事件, UniHelper.SwiperOnChange通过它来标识当前事件为Swiper的Change事件
const onChange: UniHelper.SwiperOnChange = (event) => {
  // console.log(event)
  // console.log(event.detail?.current) // 输出当前所在滑块的下标
  activeIndex.value = event.detail?.current
}
// 定义props 接收父组件传递过来的属性
defineProps<{
  list: BannerItem[]
}>()
</script>
<template>
  <view class="carousel">
    <swiper :circular="true" :autoplay="true" :interval="3000" @change="onChange">
      <swiper-item v-for="item in list" v-bind:key="item.id">
        <navigator url="/pages/index/index" hover-class="none" class="navigator">
          <img mode="aspectFill" class="image" :src="item.imgUrl" />
        </navigator>
      </swiper-item>
    </swiper>
    <!-- 指示点 -->
    <view class="indicator">
      <text
        v-for="(item, index) in list"
        :key="item.id"
        class="dot"
        :class="{ active: index === activeIndex }"
      ></text>
    </view>
  </view>
</template>
<style lang="scss">
:host {
  display: block;
  height: 320rpx;
}
/* 轮播图 */
.carousel {
  height: 100%;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  background-color: #efefef;
  .indicator {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 16rpx;
    display: flex;
    justify-content: center;
    .dot {
      width: 30rpx;
      height: 6rpx;
      margin: 0 8rpx;
      border-radius: 6rpx;
      background-color: rgba(255, 255, 255, 0.4);
    }
    .active {
      background-color: #fff;
    }
  }
  .navigator,
  .image {
    width: 100%;
    height: 100%;
  }
}
</style>
