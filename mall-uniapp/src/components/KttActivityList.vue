<script setup lang="ts">
import { getListActivity } from '@/services/seckill/activity'
import type { Activity } from '@/services/seckill/activity'
import { onMounted, ref, onUnmounted } from 'vue'

const activityList = ref<Activity[]>([])
const timers = ref<{ [key: number]: number }>({})

// 下拉刷新相关
const refreshing = ref(false)
const triggered = ref(false)

// 格式化时间
const formatTime = (time: number) => {
  const days = Math.floor(time / (24 * 60 * 60 * 1000))
  const hours = Math.floor((time % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  const minutes = Math.floor((time % (60 * 60 * 1000)) / (60 * 1000))
  const seconds = Math.floor((time % (60 * 1000)) / 1000)

  return `${days}天 ${hours}时 ${minutes}分 ${seconds}秒`
}

// 计算剩余时间
const calculateRemainingTime = (startTime: string) => {
  const start = new Date(startTime).getTime()
  const now = new Date().getTime()
  return start - now
}

// 更新倒计时
const updateCountdown = (activity: Activity) => {
  if (!activity.startTime) return ''

  const remainingTime = calculateRemainingTime(activity.startTime)
  if (remainingTime <= 0) {
    return '活动已开始'
  }
  return formatTime(remainingTime)
}

// 跳转到商品列表页面
const goToProductList = (activity: Activity) => {
  uni.navigateTo({
    url: `/pages/activity-products/index?activityId=${
      activity.id
    }&activityName=${encodeURIComponent(activity.activityName)}&startTime=${encodeURIComponent(
      activity.startTime || '',
    )}&endTime=${encodeURIComponent(activity.endTime || '')}&status=${activity.status || 0}`,
  })
}

// 获取秒杀活动列表数据
const getActivityListData = async () => {
  try {
    const res = await getListActivity()
    // The API returns { code: 200, msg: "操作成功", data: [...] }
    if (res && res.code === 200 && res.data) {
      activityList.value = res.data
      // 为每个未开始的活动启动倒计时
      activityList.value.forEach((activity) => {
        if (activity.status === 0) {
          timers.value[activity.id] = setInterval(() => {
            activityList.value = [...activityList.value]
          }, 1000)
        }
      })
    } else {
      activityList.value = []
      console.log('No activity data returned')
    }
  } catch (error) {
    console.error('Failed to fetch activity list:', error)
    activityList.value = []
  }
}

// 处理下拉刷新
const onRefresh = async () => {
  if (refreshing.value) return

  refreshing.value = true

  try {
    // 清除所有计时器
    Object.values(timers.value).forEach((timer) => {
      clearInterval(timer)
    })
    timers.value = {}

    // 重新加载数据
    await getActivityListData()

    // 显示刷新成功提示
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1000,
    })
  } catch (error) {
    console.error('刷新数据失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'none',
      duration: 1000,
    })
  } finally {
    // 延迟结束刷新状态，让用户看到刷新动画
    setTimeout(() => {
      refreshing.value = false
      triggered.value = false
    }, 1000)
  }
}

// 监听拉动结束事件
const onRestore = () => {
  triggered.value = false
}

// 监听用户下拉动作
const onPulling = (e: any) => {
  // 可以根据下拉距离实现一些动画效果
  console.log('下拉距离:', e.detail.dy)
}

// 组件挂载完毕时获取数据
onMounted(() => {
  getActivityListData()
})

// 组件卸载时清除所有定时器
onUnmounted(() => {
  Object.values(timers.value).forEach((timer) => {
    clearInterval(timer)
  })
})

// 暴露刷新方法供父组件调用
defineExpose({
  refresh: getActivityListData,
})
</script>

<template>
  <view class="activity-container" v-if="activityList && activityList.length > 0">
    <view class="caption">
      <text class="text">秒杀活动</text>
    </view>

    <!-- 使用scroll-view实现下拉刷新 -->
    <scroll-view
      scroll-y
      refresher-enabled
      :refresher-triggered="triggered"
      :refresher-threshold="80"
      refresher-background="#f4f4f4"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRestore"
      @refresherpulling="onPulling"
      class="activity-scroll"
    >
      <view class="activity-list">
        <view
          class="activity-item"
          v-for="item in activityList"
          :key="item.id"
          @tap="goToProductList(item)"
        >
          <view class="activity-image" v-if="item.activityImage">
            <image :src="item.activityImage" mode="aspectFill"></image>
          </view>
          <view class="activity-info">
            <view class="name">{{ item.activityName }}</view>
            <view class="description">{{ item.activityDesc || item.remark || '暂无描述' }}</view>
            <view class="time">
              <text class="label">活动时间: </text>
              <text
                >{{ item.startTime ? item.startTime.substring(0, 10) : '--' }} -
                {{ item.endTime ? item.endTime.substring(0, 10) : '--' }}</text
              >
            </view>
            <view class="countdown" v-if="item.status === 0">
              <text>{{ updateCountdown(item) }}</text>
            </view>
            <view
              class="status"
              :class="{
                'status-active': item.status === 1,
                'status-end': item.status === 2,
              }"
            >
              {{ item.status === 0 ? '未开始' : item.status === 1 ? '进行中' : '已结束' }}
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态提示 -->
      <view class="refresh-tip" v-if="refreshing">
        <view class="refresh-icon"></view>
        <text>数据刷新中...</text>
      </view>
    </scroll-view>
  </view>
  <view class="empty-container" v-else>
    <text class="empty-text">暂无活动</text>
    <view class="refresh-btn" @tap="onRefresh">点击刷新</view>
  </view>
</template>

<style lang="scss">
:host {
  display: block;
}
/* 分类标题 */
.caption {
  display: flex;
  justify-content: center;
  line-height: 1;
  padding: 36rpx 0 40rpx;
  font-size: 32rpx;
  color: #262626;
  .text {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 28rpx 0 30rpx;

    &::before,
    &::after {
      content: '';
      width: 20rpx;
      height: 20rpx;
      background-image: url(@/static/images/bubble.png);
      background-size: contain;
      margin: 0 10rpx;
    }
  }
}

.activity-container {
  margin-top: 20rpx;
}

/* 添加scroll-view样式 */
.activity-scroll {
  height: 800rpx; /* 根据实际情况调整高度 */
  width: 100%;
}

.activity-list {
  padding: 0 20rpx;
}

.activity-item {
  display: flex;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.activity-image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.activity-info {
  flex: 1;
  position: relative;
}

.name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding-right: 100rpx; /* 为状态标签留出空间 */
  margin-bottom: 15rpx;
}

.description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.time {
  font-size: 24rpx;
  color: #999;
  display: flex;
}

.label {
  color: #666;
}

.status {
  position: absolute;
  top: 0;
  right: 0;
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 20rpx;

  &.status-active {
    color: #fff;
    background-color: #cf4444;
  }
  &.status-end {
    color: #fff;
    background-color: #999;
  }
}

.countdown {
  font-size: 24rpx;
  color: #cf4444;
  margin-top: 10rpx;
  display: flex;
  align-items: center;

  .label {
    color: #666;
    margin-right: 10rpx;
  }
}

/* 添加刷新相关样式 */
.refresh-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

.refresh-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  border-top-color: #cf4444;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.refresh-btn {
  background-color: #cf4444;
  color: #fff;
  font-size: 28rpx;
  padding: 15rpx 40rpx;
  border-radius: 40rpx;
}
</style>
