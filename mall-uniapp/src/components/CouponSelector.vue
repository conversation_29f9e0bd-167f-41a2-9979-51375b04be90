<script setup lang="ts">
import { ref, computed } from 'vue'
import { getAvailableCouponsAPI } from '@/services/coupon'
import { useMemberStore } from '@/stores'
import type { UserCouponItem } from '@/types/coupon'
import { UserCouponStatus } from '@/constants/coupon'

// 定义props
const props = defineProps<{
  orderAmount: number
  goodsList: Array<{ goodsId: number; quantity: number }>
  selectedCouponId?: number | null
}>()

// 定义emits
const emit = defineEmits<{
  select: [couponId: number | null]
}>()

// 获取会员信息
const memberStore = useMemberStore()

// 优惠券列表
const couponList = ref<UserCouponItem[]>([])
// 加载状态
const loading = ref(false)
// 是否显示弹窗
const showPopup = ref(false)

// 获取可用优惠券
const getAvailableCoupons = async () => {
  if (loading.value) return

  try {
    loading.value = true

    const params = {
      userId: memberStore.profile.user.id,
      orderAmount: props.orderAmount,
      goodsList: props.goodsList,
    }

    const res = await getAvailableCouponsAPI(params)
    couponList.value = res.result || []
  } catch (error) {
    console.error('获取可用优惠券失败:', error)
    uni.showToast({ title: '获取优惠券失败', icon: 'error' })
  } finally {
    loading.value = false
  }
}

// 选择优惠券
const selectCoupon = (couponId: number | null) => {
  emit('select', couponId)
  showPopup.value = false
}

// 格式化优惠券类型
const formatCouponType = (coupon: UserCouponItem) => {
  if (!coupon.coupon) return ''

  switch (coupon.coupon.couponType) {
    case 1:
      return `满${coupon.coupon.minSpend}减${coupon.coupon.faceValue}`
    case 2:
      return `${coupon.coupon.discount}折`
    case 3:
      return `无门槛减${coupon.coupon.faceValue}`
    case 4:
      return '包邮券'
    default:
      return ''
  }
}

// 计算优惠金额
const calculateDiscount = (coupon: UserCouponItem) => {
  if (!coupon.coupon) return 0

  switch (coupon.coupon.couponType) {
    case 1: // 满减券
      if (props.orderAmount >= coupon.coupon.minSpend) {
        return coupon.coupon.faceValue || 0
      }
      return 0
    case 2: // 折扣券
      return props.orderAmount * (1 - (coupon.coupon.discount || 0) / 10)
    case 3: // 无门槛券
      return coupon.coupon.faceValue || 0
    case 4: // 包邮券
      return 0 // 包邮券不显示金额
    default:
      return 0
  }
}

// 打开弹窗
const openPopup = () => {
  showPopup.value = true
  getAvailableCoupons()
}

// 关闭弹窗
const closePopup = () => {
  showPopup.value = false
}

// 暴露方法给父组件
defineExpose({
  openPopup,
  closePopup,
})
</script>

<template>
  <view class="coupon-selector">
    <!-- 优惠券选择按钮 -->
    <view class="coupon-btn" @tap="openPopup">
      <text class="icon-coupon"></text>
      <text class="btn-text">
        {{ props.selectedCouponId ? '已选择优惠券' : '选择优惠券' }}
      </text>
      <text class="icon-right"></text>
    </view>

    <!-- 优惠券弹窗 -->
    <view v-if="showPopup" class="popup-overlay" @tap="closePopup">
      <view class="popup-content" @tap.stop>
        <view class="popup-header">
          <text class="popup-title">选择优惠券</text>
          <text class="popup-close" @tap="closePopup">×</text>
        </view>

        <scroll-view class="popup-body" scroll-y>
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>

          <view v-else-if="couponList.length === 0" class="empty">
            <text>暂无可用的优惠券</text>
          </view>

          <view v-else class="coupon-list">
            <!-- 不使用优惠券选项 -->
            <view
              class="coupon-item"
              :class="{ active: !props.selectedCouponId }"
              @tap="selectCoupon(null)"
            >
              <view class="coupon-info">
                <text class="coupon-type">不使用优惠券</text>
                <text class="coupon-desc">享受原价购买</text>
              </view>
              <view class="coupon-radio">
                <text class="radio-icon" :class="{ checked: !props.selectedCouponId }">●</text>
              </view>
            </view>

            <!-- 优惠券列表 -->
            <view
              v-for="coupon in couponList"
              :key="coupon.id"
              class="coupon-item"
              :class="{ active: props.selectedCouponId === coupon.id }"
              @tap="selectCoupon(coupon.id)"
            >
              <view class="coupon-info">
                <text class="coupon-type">{{ formatCouponType(coupon) }}</text>
                <text class="coupon-name">{{ coupon.coupon?.couponName }}</text>
                <text class="coupon-desc">
                  {{
                    coupon.coupon?.couponType === 4 ? '包邮' : `可省${calculateDiscount(coupon)}元`
                  }}
                </text>
              </view>
              <view class="coupon-radio">
                <text class="radio-icon" :class="{ checked: props.selectedCouponId === coupon.id }"
                  >●</text
                >
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="popup-footer">
          <view class="confirm-btn" @tap="closePopup">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.coupon-selector {
  .coupon-btn {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 8rpx;
    margin: 20rpx;
    border: 1rpx solid #eee;

    .icon-coupon {
      font-size: 32rpx;
      color: #ff6b6b;
      margin-right: 10rpx;
    }

    .btn-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .icon-right {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.popup-content {
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .popup-close {
    font-size: 40rpx;
    color: #999;
    padding: 10rpx;
  }
}

.popup-body {
  max-height: 60vh;
  padding: 20rpx;
}

.loading,
.empty {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}

.coupon-list {
  .coupon-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    margin-bottom: 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    border: 2rpx solid transparent;

    &.active {
      border-color: #3c69dc;
      background-color: #f0f8ff;
    }

    .coupon-info {
      flex: 1;

      .coupon-type {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .coupon-name {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 4rpx;
      }

      .coupon-desc {
        display: block;
        font-size: 22rpx;
        color: #999;
      }
    }

    .coupon-radio {
      margin-left: 20rpx;

      .radio-icon {
        font-size: 32rpx;
        color: #ddd;

        &.checked {
          color: #3c69dc;
        }
      }
    }
  }
}

.popup-footer {
  padding: 30rpx;
  border-top: 1rpx solid #eee;

  .confirm-btn {
    background-color: #3c69dc;
    color: #fff;
    text-align: center;
    padding: 20rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}
</style>
