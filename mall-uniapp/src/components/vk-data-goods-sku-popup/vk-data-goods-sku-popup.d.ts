import { Component } from '@uni-helper/uni-app-types'

/** 导出SKU 弹出层组件，此组件的属性为SkuPopupProps */
export type SkuPopup = Component<SkuPopupProps>

/** 导出SKU弹出层实例, 使用InstanceType来获取SkuPopup弹出层组件的实例类型 */
export type SkuPopupInstanceType = InstanceType<SkuPopup>

/** SKU 弹出层属性 */
export type SkuPopupProps = {
  /** 双向绑定，true 为打开组件，false 为关闭组件 */
  modelValue: boolean
  /** 商品信息本地数据源 */
  localdata: SkuPopupLocaldata
  /** 按钮模式 1:都显示 2:只显示购物车 3:只显示立即购买 */
  mode?: 1 | 2 | 3
  /** 该商品已抢完时的按钮文字 */
  noStockText?: string
  /** 库存文字 */
  stockText?: string
  /** 点击遮罩是否关闭组件 */
  maskCloseAble?: boolean
  /** 顶部圆角值 */
  borderRadius?: string | number
  /** 最小购买数量 */
  minBuyNum?: number
  /** 最大购买数量 */
  maxBuyNum?: number
  /** 每次点击后的数量 */
  stepBuyNum?: number
  /** 是否只能输入 step 的倍数 */
  stepStrictly?: boolean
  /** 是否隐藏库存的显示 */
  hideStock?: false
  /** 主题风格 */
  theme?: 'default' | 'red-black' | 'black-white' | 'coffee' | 'green'
  /** 默认金额会除以100（即100=1元），若设置为0，则不会除以100（即1=1元） */
  amountType?: 1 | 0
  /** 自定义获取商品信息的函数（已知支付宝不支持，支付宝请改用localdata属性） */
  customAction?: () => void
  /** 是否显示右上角关闭按钮 */
  showClose?: boolean
  /** 关闭按钮的图片地址 */
  closeImage?: string
  /** 价格的字体颜色 */
  priceColor?: string
  /** 立即购买 - 按钮的文字 */
  buyNowText?: string
  /** 立即购买 - 按钮的字体颜色 */
  buyNowColor?: string
  /** 立即购买 - 按钮的背景颜色 */
  buyNowBackgroundColor?: string
  /** 加入购物车 - 按钮的文字 */
  addCartText?: string
  /** 加入购物车 - 按钮的字体颜色 */
  addCartColor?: string
  /** 加入购物车 - 按钮的背景颜色 */
  addCartBackgroundColor?: string
  /** 商品缩略图背景颜色 */
  goodsThumbBackgroundColor?: string
  /** 样式 - 不可点击时,按钮的样式 */
  disableStyle?: object
  /** 样式 - 按钮点击时的样式 */
  activedStyle?: object
  /** 样式 - 按钮常态的样式 */
  btnStyle?: object
  /** 字段名 - 商品表id的字段名 */
  goodsIdName?: string
  /** 字段名 - sku表id的字段名 */
  skuIdName?: string
  /** 字段名 - 商品对应的sku列表的字段名 */
  skuListName?: string
  /** 字段名 - 商品规格名称的字段名 */
  specListName?: string
  /** 字段名 - sku库存的字段名 */
  stockName?: string
  /** 字段名 - sku组合路径的字段名 */
  skuArrName?: string
  /** 字段名 - 商品缩略图字段名(未选择sku时) */
  goodsThumbName?: string
  /** 被选中的值 */
  selectArr?: string[]

  /** 打开弹出层 */
  onOpen: () => void
  /** 关闭弹出层 */
  onClose: () => void
  /** 点击加入购物车时（需选择完SKU才会触发）*/
  onAddCart: (event: SkuPopupEvent) => void
  /** 点击立即购买时（需选择完SKU才会触发）*/
  onBuyNow: (event: SkuPopupEvent) => void
}

/**  商品信息本地数据源(可以参考GoodsItem商品信息实体类) */
export type SkuPopupLocaldata = {
  /** 商品 ID */
  _id: number
  /** 商品名称 */
  goodsName: string
  /** 商品图片 */
  goods_thumb: string
  /** 商品的可选规格属性集合 */
  spec_list: AttrItem[]
  /** 商品SKU列表 */
  sku_list: SkuItem[]
}

/** 商品SKU列表,对应后端项目中的SkuItem实体类 */
export type SkuItem = {
  /** SKU ID */
  _id: number
  /**  商品 ID */
  goods_id: number
  /** 商品名称 */
  goods_name: string
  /** 商品图片 */
  image: string
  /** SKU 价格 * 100, 注意：需要乘以 100 */
  price: number
  /** SKU 的规格属性列表 */
  sku_name_arr: string[]
  /** SKU 库存 */
  stock: number
}

/** 商品规格名称的集合 */
export type AttrItem = {
  /** 规格属性名称 */
  name: string
  /** 可选规格属性值集合,对应AttrItem类中的private List<AttrValues> values; */
  list: AttrValues[]
}

/** 可选规格属性值信息(即SPU的规格属性值对象)，对应后端项目中的AttrValues实体类 */
export type AttrValues = {
  /** 可选规格属性值备注 */
  desc: string
  /** 可选规格属性值名称 */
  name: string
  /** 可选规格属性值图片链接 */
  picture: string
}

/** 当前选择的sku数据，&是并且，即当前选择的SKU数据有SkuItem商品sku列表，以及buy_num购买数量 */
export type SkuPopupEvent = SkuItem & {
  /** 商品购买数量 */
  buy_num: number
}

/** 全局组件类型声明 */
declare module 'vue' {
  // 将SkuPopup作为一个全局组件进行导出，组件名为'vk-data-goods-sku-popup'
  export interface GlobalComponents {
    'vk-data-goods-sku-popup': SkuPopup
  }
}
