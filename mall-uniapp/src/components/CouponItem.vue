<script setup lang="ts">
// @ts-nocheck
import { ref } from "vue"
import type { CouponItem, CouponType } from '@/types/coupon'

// 扩展CouponItem接口以支持前端显示需要的字段
interface ExtendedCouponItem extends Omit<CouponItem, 'startTime' | 'endTime'> {
  startTime: number // 前端使用时间戳
  endTime: number   // 前端使用时间戳
  claimed?: boolean // 前端使用：是否已领取
  useStatus?: number // 添加使用状态字段，用于"我的优惠券"模式
}

// 定义组件props
interface Props {
  coupon: ExtendedCouponItem
  mode?: 'claim' | 'owned' // 模式：claim=领取模式，owned=我的优惠券模式
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'claim'
})

// 定义组件事件
const emit = defineEmits<{
  claim: [coupon: ExtendedCouponItem] // 领取优惠券事件
}>()

// 展开状态
const isExpanded = ref(false)

// 获取优惠券金额显示
const getCouponAmount = (coupon: ExtendedCouponItem) => {
  switch (coupon.couponType) {
    case 1:
    case 3:
      return coupon.faceValue || 0
    case 2:
      return coupon.discount || 0
    case 4:
      return "包邮"
    default:
      return 0
  }
}

// 获取优惠券类型标签
const getCouponTypeLabel = (coupon: ExtendedCouponItem) => {
  switch (coupon.couponType) {
    case 1:
      return "现金券"
    case 2:
      return "折扣券"
    case 3:
      return "现金券"
    case 4:
      return ""
    default:
      return "优惠券"
  }
}

// 格式化有效期
const formatValidTime = (coupon: ExtendedCouponItem) => {
  const e = new Date(coupon.endTime)
  const p = (n: number) => String(n).padStart(2, "0")
  return `有效期至 ${e.getFullYear()}-${p(e.getMonth() + 1)}-${p(e.getDate())}`
}

// 检查优惠券是否可领取
const isCouponClaimable = (coupon: ExtendedCouponItem) => {
  const now = Date.now()
  return coupon.remainCount > 0 && now <= coupon.endTime && !coupon.claimed
}

// 获取按钮文本和状态
const getButtonInfo = (coupon: ExtendedCouponItem) => {
  if (props.mode === 'claim') {
    // 领取模式
    if (isCouponClaimable(coupon)) {
      return { text: '立即领取', disabled: false, type: 'primary' }
    } else if (coupon.claimed) {
      return { text: '已领取', disabled: true, type: 'disabled' }
    } else {
      return { text: '已抢完', disabled: true, type: 'disabled' }
    }
  } else {
    // 我的优惠券模式
    const now = Date.now()
    
    // 检查是否有useStatus属性（从UserCouponItem转换来的数据）
    if (coupon.useStatus !== undefined) {
      if (coupon.useStatus === 0) { // UserCouponStatus.UNUSED
        // 检查是否过期
        if (now > coupon.endTime) {
          return { text: '已过期', disabled: true, type: 'expired' }
        } else {
          return { text: '未使用', disabled: false, type: 'available' }
        }
      } else if (coupon.useStatus === 1) { // UserCouponStatus.USED
        return { text: '已使用', disabled: true, type: 'used' }
      } else if (coupon.useStatus === 2) { // UserCouponStatus.EXPIRED
        return { text: '已过期', disabled: true, type: 'expired' }
      }
    }
    
    // 兜底逻辑（原有逻辑）
    if (coupon.claimed === false) {
      if (now > coupon.endTime) {
        return { text: '已过期', disabled: true, type: 'expired' }
      } else {
        return { text: '未使用', disabled: false, type: 'available' }
      }
    } else if (now > coupon.endTime) {
      return { text: '已过期', disabled: true, type: 'expired' }
    } else {
      return { text: '已使用', disabled: true, type: 'used' }
    }
  }
}

// 切换展开状态 - 添加防抖优化
let toggleTimer: NodeJS.Timeout | null = null
const toggleDetails = () => {
  if (toggleTimer) {
    clearTimeout(toggleTimer)
  }
  
  toggleTimer = setTimeout(() => {
    isExpanded.value = !isExpanded.value
    toggleTimer = null
  }, 50) // 50ms防抖
}

// 处理领取点击
const handleClaim = () => {
  if (isCouponClaimable(props.coupon)) {
    emit('claim', props.coupon)
  }
}
</script>

<template>
  <view 
    class="coupon-item" 
    :class="{ disabled: !isCouponClaimable(coupon) }"
  >
    <view class="coupon-left">
      <view class="amount-section">
        <text class="currency">¥</text>
        <text class="amount">{{ getCouponAmount(coupon) }}</text>
        <text class="coupon-type-label">{{ getCouponTypeLabel(coupon) }}</text>
      </view>
      <view class="condition" v-if="coupon.minSpend">
        满{{ coupon.minSpend }}元可用
      </view>
      <view class="condition" v-else-if="coupon.couponType === 2">
        全场通用
      </view>
      <view class="condition" v-else>
        无门槛使用
      </view>
      <view class="coupon-name-row">
        <text class="coupon-name">{{ coupon.couponName }}</text>
        <view class="action-row">
          <view
            class="claim-btn"
            :class="[getButtonInfo(coupon).type, { disabled: getButtonInfo(coupon).disabled }]"
            @tap="getButtonInfo(coupon).disabled ? null : handleClaim"
          >
            {{ getButtonInfo(coupon).text }}
          </view>
          <view class="expand-btn" @tap="toggleDetails">
            <text class="expand-icon" :class="{ expanded: isExpanded }">▼</text>
          </view>
        </view>
      </view>
      <view class="details" v-if="isExpanded">
        <view class="detail-item">不能与其他优惠券同时使用；</view>
        <view class="detail-item">仅限购买自营商品时使用；</view>
        <view class="detail-item">所有商品上架后即可使用该券；</view>
      </view>
      <view class="valid-time">{{ formatValidTime(coupon) }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.coupon-item {
  background: #fff;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  
  &.disabled {
    opacity: 0.6;
    .coupon-left {
      .amount {
        color: #999;
      }
      .coupon-name {
        color: #999;
      }
    }
  }
  
  .coupon-left {
    padding: 16rpx 20rpx;
    display: flex;
    flex-direction: column;
    
    .amount-section {
      display: flex;
      align-items: baseline;
      margin-bottom: 8rpx;
      
      .currency {
        font-size: 20rpx;
        color: #ff4d4f;
        font-weight: bold;
      }
      
      .amount {
        font-size: 48rpx;
        color: #ff4d4f;
        font-weight: bold;
        line-height: 1;
      }
      
      .coupon-type-label {
        font-size: 20rpx;
        color: #ff4d4f;
        margin-left: 6rpx;
        font-weight: 500;
      }
    }
    
    .condition {
      font-size: 22rpx;
      color: #999;
      margin-bottom: 12rpx;
    }
    
    .coupon-name-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10rpx;
      
      .coupon-name {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
        flex: 1;
      }
      
      .action-row {
        display: flex;
        align-items: center;
        gap: 12rpx;
        
        .claim-btn {
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
          font-size: 20rpx;
          font-weight: 500;
          border: none;
          white-space: nowrap;
          
          // 主要按钮样式（立即领取）
          &.primary {
            background: #ff4d4f;
            color: #fff;
          }
          
          // 可用状态（未使用）
          &.available {
            background: #52c41a;
            color: #fff;
          }
          
          // 已使用状态
          &.used {
            background: #f5f5f5;
            color: #999;
          }
          
          // 已过期状态
          &.expired {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1rpx solid #ffccc7;
          }
          
          // 禁用状态（已领取、已抢完）
          &.disabled {
            background: #f5f5f5;
            color: #999;
          }
        }
        
        .expand-btn {
          padding: 4rpx 8rpx;
          
          .expand-icon {
            font-size: 20rpx;
            color: #999;
            transition: transform 0.3s ease;
            display: inline-block;
            
            &.expanded {
              transform: rotate(180deg);
            }
          }
        }
      }
    }
    
    .details {
      font-size: 20rpx;
      color: #999;
      line-height: 1.3;
      margin-bottom: 8rpx;
      overflow: hidden;
      transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateZ(0); /* 启用硬件加速 */
      will-change: max-height, opacity; /* 优化动画性能 */
      
      .detail-item {
        margin-bottom: 2rpx;
        position: relative;
        padding-left: 12rpx;
        
        &::before {
          content: "·";
          position: absolute;
          left: 0;
          color: #ccc;
        }
      }
    }
    
    .valid-time {
      font-size: 20rpx;
      color: #999;
      margin-top: 8rpx;
    }
  }
}

</style>