<template>
  <div
    ref="sliderContainer"
    @touchstart="onTouchStart"
    @touchmove="onTouchMove"
    @touchend="onTouchEnd"
    @mousedown="onMouseDown"
    @mousemove="onMouseMove"
    @mouseup="onMouseUp"
    @mouseleave="onMouseLeave"
    class="slider-verify-container"
  >
    <span v-if="blockState === 0">请按住滑块，拖动到最右边</span>
    <div :style="{ left: leftP }" class="slider-verify-block"></div>
    <div :style="{ width: leftP }" class="slider-verify-complete">
      <span v-if="blockState === 2">验证成功</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['success', 'failed'])
const leftP = ref('0')
const blockState = ref(0)
const startP = ref(undefined)
const sliderContainer = ref(undefined)
const isDragging = ref(false)

// 鼠标事件处理
const onMouseDown = (e) => {
  if (blockState.value !== 2) {
    leftP.value = '0'
    blockState.value = 1
    isDragging.value = true
    startP.value = { clientX: e.clientX }
  } else {
    leftP.value = '0'
    blockState.value = 0
  }
}

const onMouseMove = (e) => {
  if (blockState.value === 1 && isDragging.value) {
    let width = e.clientX - startP.value.clientX
    if (width + 56 > 400) {
      leftP.value = 400 - 56 + 'px'
      blockState.value = 2
      isDragging.value = false
      emit('success')
    } else if (width > 0) {
      leftP.value = width + 'px'
    }
  }
}

const onMouseUp = (e) => {
  if (blockState.value !== 2 && isDragging.value) {
    leftP.value = '0'
    blockState.value = 0
    isDragging.value = false
    emit('failed')
  }
}

const onMouseLeave = (e) => {
  if (blockState.value !== 2 && isDragging.value) {
    leftP.value = '0'
    blockState.value = 0
    isDragging.value = false
    emit('failed')
  }
}

// 触摸事件处理（移动端）
const onTouchStart = (e) => {
  if (blockState.value !== 2) {
    leftP.value = '0'
    blockState.value = 1
    isDragging.value = true
    const touch = e.touches[0]
    startP.value = { clientX: touch.clientX }
  } else {
    leftP.value = '0'
    blockState.value = 0
  }
}

const onTouchMove = (e) => {
  if (blockState.value === 1 && isDragging.value) {
    e.preventDefault() // 防止页面滚动
    const touch = e.touches[0]
    let width = touch.clientX - startP.value.clientX
    if (width + 56 > 400) {
      leftP.value = 400 - 56 + 'px'
      blockState.value = 2
      isDragging.value = false
      emit('success')
    } else if (width > 0) {
      leftP.value = width + 'px'
    }
  }
}

const onTouchEnd = (e) => {
  if (blockState.value !== 2 && isDragging.value) {
    leftP.value = '0'
    blockState.value = 0
    isDragging.value = false
    emit('failed')
  }
}

// 暴露刷新方法
const refresh = () => {
  leftP.value = '0'
  blockState.value = 0
  isDragging.value = false
}

defineExpose({
  refresh,
})
</script>

<style lang="scss" scoped>
.slider-verify-container {
  width: 100%;
  height: 56px;
  background-color: transparent;
  position: relative;
  border: solid 1px #20cccf;
  text-align: center;
  color: #20cccf;
  line-height: 56px;
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  border-radius: 8px;
  overflow: hidden;
}

.slider-verify-block {
  width: 56px;
  height: 56px;
  background-color: white;
  position: absolute;
  left: 0;
  top: -1px;
  border: solid 1px #20cccf;
  background-size: 50%;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: left 0.1s ease;
}

.slider-verify-complete {
  width: 0;
  height: 56px;
  position: absolute;
  background-color: #00e6f14f;
  left: 0;
  top: 0;
  transition: width 0.1s ease;
}
</style>
