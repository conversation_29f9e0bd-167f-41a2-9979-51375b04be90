<script setup lang="ts">
//
import { getHomeGoodsGuessLikeAPI } from '@/services/home'
import type { GuessItem } from '@/types/home'
import { onMounted, ref } from 'vue'

const guessList = ref<GuessItem[]>([])
// 当前页码
let currPageNo = 1
// finish用来是否结束分页查询(最后一页)，true表示已经是最后一页，没有数据了。
const finish = ref(false)
// 获取猜你喜欢数据
const getHomeGoodsGuessLikeData = async () => {
  if (finish.value === true) {
    // true表示已经是最后一页，没有数据了。
    return uni.showToast({ icon: 'none', title: '没有数据了!' })
  }
  // 调用getHomeGoodsGuessLikeAPI()传入当前页码currPageNo和每页显示的数据行数6
  const res = await getHomeGoodsGuessLikeAPI(currPageNo, 6)
  // res.result 得到的从后端响应过来的Page分页数据。
  // Page分页工具类中有list属性为查询到的商品分页列表数据，代码为res.result.list
  // ...为对象的扩展运算符，用于取出参数对象中的所有可遍历属性，再使用push添加到guessList中
  guessList.value.push(...res.result.list)
  if (currPageNo < res.result.totalPageCount) {
    // 如果当前页码小于总的页数totalPageCount，就对当前页码进行累加1
    currPageNo++
  } else {
    // 如果当前页码不小于总的页数，表示显示的是最后一页的数据，即没有数据了。
    finish.value = true
  }
}
// 组件挂载完毕的生命周期钩子函数为onMounted
onMounted(() => {
  // 传入一个回调函数，在回调函数内部我们调用getHomeGoodsGuessLikeData()事件
  getHomeGoodsGuessLikeData()
})
// 在猜你喜欢组件中暴露方法，供src/paegs/index/index.vue首页进行调用。
defineExpose({
  // 将getHomeGoodsGuessLikeData()事件暴露，供父组件index.vue首页进行调用。
  // 同时暴露时为了方便父组件调用，可以取一个短一点的事件名，如getMore
  getMore: getHomeGoodsGuessLikeData,
})
</script>
<template>
  <!-- 猜你喜欢 -->
  <view class="caption">
    <text class="text">猜你喜欢</text>
  </view>
  <view class="guess">
    <navigator
      class="guess-item"
      v-for="item in guessList"
      :key="item.id"
      :url="`/pages/goods/goods?id=${item.id}`"
    >
      <image class="image" mode="aspectFill" :src="item.goodsImg"></image>
      <view class="name">{{ item.goodsName }}</view>
      <view class="price">
        <text class="small">¥</text>
        <text>{{ item.goodsPrice }}</text>
      </view>
    </navigator>
  </view>
  <view class="loading-text">
    {{ finish ? '没有更多数据~' : '正在加载中...' }}
  </view>
</template>

<style lang="scss">
:host {
  display: block;
}
/* 分类标题 */
.caption {
  display: flex;
  justify-content: center;
  line-height: 1;
  padding: 36rpx 0 40rpx;
  font-size: 32rpx;
  color: #262626;
  .text {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 28rpx 0 30rpx;

    &::before,
    &::after {
      content: '';
      width: 20rpx;
      height: 20rpx;
      background-image: url(@/static/images/bubble.png);
      background-size: contain;
      margin: 0 10rpx;
    }
  }
}

/* 猜你喜欢 */
.guess {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20rpx;
  .guess-item {
    width: 345rpx;
    padding: 24rpx 20rpx 20rpx;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    overflow: hidden;
    background-color: #fff;
  }
  .image {
    width: 304rpx;
    height: 304rpx;
  }
  .name {
    height: 75rpx;
    margin: 10rpx 0;
    font-size: 26rpx;
    color: #262626;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .price {
    line-height: 1;
    padding-top: 4rpx;
    color: #cf4444;
    font-size: 26rpx;
  }
  .small {
    font-size: 80%;
  }
}
// 加载提示文字
.loading-text {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 0;
}
</style>
