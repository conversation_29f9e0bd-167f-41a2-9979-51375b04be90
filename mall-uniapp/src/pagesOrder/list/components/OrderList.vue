<script setup lang="ts">
// 获取屏幕边界到安全区域距离
import type { WechatRefundParams } from '@/types/pay'

const { safeAreaInsets } = uni.getSystemInfoSync()
import { deleteByIdMemberOrderAPI, getMemberOrderAPI } from '@/services/order'
import type { OrderListParams, OrderResult } from '@/types/order'
import { onMounted, ref, computed, onUnmounted } from 'vue'
// orderStateList为订单状态枚举值
import { orderStateList, OrderState } from '@/services/constants'

// 定义 props
const props = defineProps<{
  orderState: number
}>()

import { useMemberStore } from '@/stores'
import { getWxPayAPI, getWxWechatRefundAPI, queryPayStatusAPI } from '@/services/pay'
import { orderAgainPurchase } from '@/services/order'
// 获取登录用户信息Store
const memberStore = useMemberStore()

// 倒计时相关
const countdownMap = ref<Map<number, number>>(new Map()) // 存储每个订单的倒计时
const countdownTimers = ref<Map<number, number>>(new Map()) // 存储定时器ID

// 根据创建时间计算剩余时间
const calculateRemainingTime = (createTime: string) => {
  const createDate = new Date(createTime)
  const now = new Date()
  const timeDiff = now.getTime() - createDate.getTime()
  const remainingMs = 10 * 60 * 1000 - timeDiff // 10分钟减去已经过去的时间
  return Math.max(0, Math.floor(remainingMs / 1000))
}

// 计算倒计时显示
const getCountdownDisplay = (orderId: number) => {
  const seconds = countdownMap.value.get(orderId) || 0
  if (seconds <= 0) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 开始倒计时
const startCountdown = (orderId: number, createTime: string) => {
  // 清除之前的定时器
  if (countdownTimers.value.has(orderId)) {
    clearInterval(countdownTimers.value.get(orderId)!)
  }

  // 根据创建时间计算剩余时间
  const remainingSeconds = calculateRemainingTime(createTime)

  // 如果剩余时间小于等于0，不启动倒计时
  if (remainingSeconds <= 0) {
    countdownMap.value.delete(orderId)
    return
  }

  countdownMap.value.set(orderId, remainingSeconds)

  // 启动定时器
  const timerId = setInterval(() => {
    const currentSeconds = countdownMap.value.get(orderId) || 0
    if (currentSeconds <= 1) {
      // 倒计时结束
      clearInterval(timerId)
      countdownTimers.value.delete(orderId)
      countdownMap.value.delete(orderId)
      // 重新请求后端接口加载数据
      getMemberOrderData()
      uni.showToast({ title: '支付超时，订单已自动取消', icon: 'none' })
    } else {
      countdownMap.value.set(orderId, currentSeconds - 1)
    }
  }, 1000)

  countdownTimers.value.set(orderId, timerId)
}

// 停止倒计时
const stopCountdown = (orderId: number) => {
  if (countdownTimers.value.has(orderId)) {
    clearInterval(countdownTimers.value.get(orderId)!)
    countdownTimers.value.delete(orderId)
    countdownMap.value.delete(orderId)
  }
}

// 停止所有倒计时
const stopAllCountdowns = () => {
  countdownTimers.value.forEach((timerId) => {
    clearInterval(timerId)
  })
  countdownTimers.value.clear()
  countdownMap.value.clear()
}

// 请求参数
const queryParams: OrderListParams = {
  currPageNo: 1,
  pageSize: 5,
  orderState: props.orderState,
  userId: memberStore.profile.user.id,
}

// 获取订单列表
let orderList = ref<OrderResult[]>([])
const getMemberOrderData = async () => {
  const res = await getMemberOrderAPI(queryParams)
  // 适配后端返回的数据结构
  orderList.value = res.result.rows

  // 为待付款订单启动倒计时
  orderList.value.forEach((order) => {
    if (order.orderState === OrderState.DaiFuKuan) {
      startCountdown(order.orderId, order.createTime)
    }
  })
}

onMounted(() => {
  getMemberOrderData()
})

// 组件卸载时清除所有定时器
onUnmounted(() => {
  stopAllCountdowns()
})

// 下拉刷新订单列表
const finish = ref(false)
const onScrolltolower = async () => {
  queryParams.currPageNo++
  if (finish.value === true) {
    return uni.showToast({ icon: 'none', title: '没有数据了!' })
  }
  const res = await getMemberOrderAPI(queryParams)
  orderList.value.push(...res.result.rows)
  // 判断是否还有更多数据
  if (orderList.value.length >= res.result.total) {
    finish.value = true
  }
}

//再次购买
const addOrderAgainPurchase = async (orderId: number) => {
  try {
    uni.showModal({
      title: '提示',
      content: '确认要再次购买吗？',
      success: async (res) => {
        if (res.confirm) {
          let param = {
            opr: 'orderAgainPurchase',
            userId: memberStore.profile.user.id,
            orderId: orderId,
          }
          const res = await orderAgainPurchase(param)
          if (res.code === 200) {
            uni.showToast({ title: '下单成功', icon: 'success' })
          } else {
            uni.showToast({ title: '下单失败', icon: 'error' })
          }
        }
      },
    })
  } catch {
    return
  }
}

// 去支付按钮点击事件
const onOrderPay = async (orderId: number) => {
  let data = { opr: 'prepay', userId: memberStore.profile.user.id, orderId: orderId }
  const res = await getWxPayAPI(data)
  if (res.code != 200) {
    uni.showToast({ title: '支付失败', icon: 'error' })
    return
  }
  await wx.requestPayment({
    timeStamp: res.data.timeStamp,
    nonceStr: res.data.nonceStr,
    package: res.data.package,
    signType: 'MD5',
    paySign: res.data.paySign,
    success: async function () {
      try {
        // 支付成功后，主动查询支付状态
        const currentOrder = orderList.value.find((o) => o.orderId === orderId)
        if (currentOrder?.orderSn) {
          const payStatusRes = await queryPayStatusAPI(currentOrder.orderSn)
          console.log('支付状态查询结果:', payStatusRes)
        }
      } catch (error) {
        console.error('查询支付状态失败:', error)
      }
      // 停止倒计时
      stopCountdown(orderId)
      uni.redirectTo({ url: `/pagesOrder/payment/payment?id=${orderId}` })
    },
  })
}

// 确认收货
const onConfirmReceipt = async (orderId: number) => {
  try {
    await uni.showModal({
      title: '提示',
      content: '确认要收货吗？',
    })
    // TODO: 调用确认收货API
    uni.showToast({ title: '确认收货成功' })
    // 刷新订单列表
    getMemberOrderData()
  } catch {
    return
  }
}

// 删除订单
const onDeleteOrder = async (orderId: number) => {
  try {
    uni.showModal({
      title: '提示',
      content: '确认删除订单？',
      success: async (res) => {
        if (res.confirm) {
          // 这里添加取消订单的API调用
          let param = { opr: 'deleteOrder', orderId: orderId }
          const res = await deleteByIdMemberOrderAPI(param)
          if (res.code != 200) {
            uni.showToast({ title: res.msg || '删除订单失败', icon: 'error' })
            return
          } else {
            uni.showToast({ title: '删除订单成功', icon: 'success' })
            // 刷新订单列表
            getMemberOrderData()
          }
        }
      },
    })
  } catch {
    return
  }
}

// 显示规格详情
const showSpecDetail = (text: string) => {
  if (!text) return
  // 使用模态框替代toast，提高可读性
  uni.showModal({
    title: '商品规格',
    content: text,
    showCancel: false,
    confirmText: '关闭',
  })
}

// 处理退款请求
const onRefund = async (orderSn: string, actualPrice: number) => {
  try {
    uni.showModal({
      title: '提示',
      content: '确认要申请退款吗？',
      success: async (res) => {
        if (res.confirm) {
          // 转换金额为整数分
          const totalFeeInCents = Math.round(actualPrice * 100)

          const param: WechatRefundParams = {
            data: {
              outTradeNo: orderSn,
              totalFee: totalFeeInCents.toString(), // 直接使用整数
              refundFee: totalFeeInCents.toString(), // 直接使用整数
              refundDesc: '11',
            },
          }

          console.log('退款请求参数:', param) // 添加日志

          const res = await getWxWechatRefundAPI(param)
          if (res.code === 200) {
            uni.showToast({ title: '退款申请成功', icon: 'success' })
            // 刷新订单列表
            getMemberOrderData()
          } else {
            uni.showToast({
              title: res.msg || '退款申请失败',
              icon: 'error',
              duration: 2000,
            })
          }
        }
      },
    })
  } catch (error) {
    console.error('退款请求失败:', error)
    uni.showToast({
      title: '退款请求发生错误',
      icon: 'error',
      duration: 2000,
    })
  }
}
</script>

<template>
  <scroll-view @scrolltolower="onScrolltolower" scroll-y class="orders">
    <view class="empty-state" v-if="orderList.length === 0">
      <image src="/static/images/empty-order.png" mode="aspectFit"></image>
      <text>暂无相关订单</text>
    </view>

    <navigator
      class="card"
      v-for="order in orderList"
      :key="order.orderId"
      :url="`/pagesOrder/detail/detail?id=${order.orderId}`"
      hover-class="none"
    >
      <!-- 订单信息 -->
      <view class="status">
        <view class="status-left">
          <text class="order-sn">订单号: {{ order.orderSn }}</text>
          <text class="date">{{ order.createTime }}</text>
        </view>
        <view class="status-right">
          <text class="state-text">{{ orderStateList[order.orderState].text }}</text>
          <text
            v-if="order.orderState >= OrderState.DaiPingJia"
            class="icon-delete"
            @tap.stop="onDeleteOrder(order.orderId)"
            >删除
          </text>
        </view>
      </view>

      <!-- 商品信息 -->
      <navigator
        v-for="item in order.orderDetails"
        :key="item.detailId"
        class="goods"
        :url="`/pages/goods/goods?id=${item.goodsId}`"
        hover-class="none"
      >
        <view class="cover">
          <image
            mode="aspectFill"
            :src="item.goodsImg || '/static/images/default-product.png'"
          ></image>
        </view>
        <view class="meta">
          <view class="name ellipsis">{{ item.goodsName || '商品名称' }}</view>
          <view class="type" v-if="item.attrsText" @tap.stop="showSpecDetail(item.attrsText)">
            <text class="type-text">{{ item.attrsText }}</text>
            <text class="type-more" v-if="item.attrsText && item.attrsText.length > 10">...</text>
          </view>
          <view class="price-quantity">
            <text class="price">¥{{ item.sellingPrice || 0 }}</text>
            <text class="quantity-tag">x{{ item.quantity }}</text>
          </view>
        </view>
      </navigator>

      <!-- 支付信息 -->
      <view class="payment">
        <view class="payment-info">
          <text class="count">共{{ order.orderDetails?.length || 0 }}件商品</text>
          <text>实付</text>
          <text class="amount">
            <text class="symbol">¥</text>
            {{ order.actualPrice }}
          </text>
        </view>
      </view>

      <!-- 订单操作按钮 -->
      <view class="action">
        <!-- 待付款 -->
        <template v-if="order.orderState === OrderState.DaiFuKuan">
          <view class="countdown-info" v-if="getCountdownDisplay(order.orderId) !== '00:00'">
            <text class="countdown-text">支付剩余时间</text>
            <text class="countdown-time">{{ getCountdownDisplay(order.orderId) }}</text>
          </view>
          <view class="button primary" @tap.stop="onOrderPay(order.orderId)">去支付</view>
        </template>
        <!-- 待发货 -->
        <template v-else-if="order.orderState === OrderState.DaiFaHuo">
          <view class="button secondary" @tap.stop="onRefund(order.orderSn, order.actualPrice)"
            >申请退款</view
          >
          <view class="button secondary">提醒发货</view>
        </template>
        <!-- 待收货 -->
        <template v-else-if="order.orderState === OrderState.DaiShouHuo">
          <view class="button primary" @tap.stop="onConfirmReceipt(order.orderId)">确认收货</view>
          <view class="button secondary">查看物流</view>
        </template>
        <!-- 待评价 -->
        <template v-else-if="order.orderState === OrderState.DaiPingJia">
          <navigator
            class="button primary"
            :url="`/pagesOrder/comment/comment?id=${order.orderId}`"
            hover-class="none"
          >
            去评价
          </navigator>
          <view
            class="button secondary"
            @tap.stop="addOrderAgainPurchase(order.orderId)"
            hover-class="none"
          >
            再次购买
          </view>
        </template>
        <!-- 已完成/已取消 -->
        <template v-else>
          <view
            class="button secondary"
            @tap.stop="addOrderAgainPurchase(order.orderId)"
            hover-class="none"
          >
            再次购买
          </view>
        </template>
      </view>
    </navigator>

    <!-- 底部提示文字 -->
    <view class="loading-text" :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' }">
      {{ finish ? '没有更多数据~' : '正在加载...' }}
    </view>
  </scroll-view>
</template>

<style lang="scss">
.orders {
  background-color: #f7f7f8;
  min-height: 100%;

  .empty-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 160rpx;

    image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 30rpx;
    }

    text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .card {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.03);
    overflow: hidden;
  }

  .status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .status-left {
      display: flex;
      flex-direction: column;

      .order-sn {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 8rpx;
      }

      .date {
        font-size: 22rpx;
        color: #999;
      }
    }

    .status-right {
      display: flex;
      align-items: center;

      .state-text {
        font-size: 28rpx;
        font-weight: 500;
        color: #3c69dc;
      }

      .icon-delete {
        margin-left: 20rpx;
        font-size: 24rpx;
        color: #999;
        padding: 6rpx 12rpx;
        border: 1rpx solid #eee;
        border-radius: 20rpx;
      }
    }
  }

  .goods {
    display: flex;
    padding: 20rpx 24rpx;
    position: relative;

    &:not(:last-child) {
      border-bottom: 1rpx solid #f5f5f5;
    }

    .cover {
      width: 160rpx;
      height: 160rpx;
      margin-right: 20rpx;
      border-radius: 8rpx;
      overflow: hidden;
      background-color: #f5f5f5;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .meta {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .name {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        margin-bottom: 10rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .type {
        display: inline-block;
        font-size: 24rpx;
        color: #888;
        background-color: #f7f7f8;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
        margin-bottom: 10rpx;
        max-width: 400rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;

        &:active {
          background-color: #eef1f8;
        }

        .type-text {
          display: inline-block;
          max-width: 380rpx;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .type-more {
          position: absolute;
          right: 8rpx;
          color: #3c69dc;
        }
      }

      .price-quantity {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price {
          font-size: 28rpx;
          color: #cf4444;
          font-weight: 500;
        }

        .quantity-tag {
          font-size: 24rpx;
          color: #fff;
          background-color: #3c69dc;
          padding: 2rpx 12rpx;
          border-radius: 20rpx;
        }
      }
    }
  }

  .payment {
    padding: 20rpx 24rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .payment-info {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .count {
        font-size: 24rpx;
        color: #999;
        margin-right: auto;
      }

      text {
        font-size: 26rpx;
        color: #666;
      }

      .amount {
        font-size: 30rpx;
        color: #cf4444;
        font-weight: 500;
        margin-left: 10rpx;

        .symbol {
          font-size: 24rpx;
        }
      }
    }
  }

  .action {
    display: flex;
    justify-content: flex-end;
    padding: 20rpx 24rpx;
    flex-wrap: wrap;

    .countdown-info {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20rpx;
      padding: 16rpx;
      background-color: #fff5f5;
      border-radius: 20rpx;
      border: 1rpx solid #ffebeb;

      .countdown-text {
        font-size: 24rpx;
        color: #cf4444;
        margin-right: 10rpx;
      }

      .countdown-time {
        font-size: 28rpx;
        font-weight: bold;
        color: #cf4444;
        font-family: 'Courier New', monospace;
        background-color: #fff;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        border: 1rpx solid #ffcdd2;
      }
    }

    .button {
      min-width: 160rpx;
      height: 60rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20rpx;
      border-radius: 30rpx;
      font-size: 26rpx;
      transition: all 0.2s;
    }

    .secondary {
      color: #3c69dc;
      border: 1rpx solid #3c69dc;
      background-color: rgba(60, 105, 220, 0.05);

      &:active {
        background-color: rgba(60, 105, 220, 0.1);
      }
    }

    .primary {
      color: #fff;
      background: linear-gradient(to right, #3c69dc, #5c89fc);
      border: none;

      &:active {
        opacity: 0.9;
      }
    }
  }

  .loading-text {
    text-align: center;
    font-size: 26rpx;
    color: #999;
    padding: 30rpx 0;
  }
}
</style>
