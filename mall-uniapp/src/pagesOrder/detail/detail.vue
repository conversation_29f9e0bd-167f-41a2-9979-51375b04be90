<script setup lang="ts">
import { useGuessList } from '@/composables'
import { ref, computed, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMemberStore } from '@/stores'
import type { OrderResult } from '@/types/order'
import {
  getMemberOrderByIdAPI,
  cancelMemberOrderAPI,
  deleteByIdMemberOrderAPI,
} from '@/services/order'
import { getWxPayAPI, queryPayStatusAPI } from '@/services/pay'
import { seckillOrderPrepay } from '@/services/seckill/order'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()
// 猜你喜欢
const { guessRef, onScrolltolower } = useGuessList()
// 获取登录用户信息Store
const memberStore = useMemberStore()

// 获取页面参数
const query = defineProps<{
  id: number
  orderType?: string
  orderSn?: string
}>()

// 获取订单详情
const order = ref<OrderResult>()
const loading = ref(true)

// 倒计时相关
const countdown = ref(0) // 剩余秒数
const countdownTimer = ref<number | null>(null) // 定时器ID

// 秒杀订单轮询相关
const pollingTimer = ref<number | null>(null) // 轮询定时器ID
const pollingCount = ref(0) // 轮询次数
const MAX_POLLING_COUNT = 30 // 最大轮询次数（30秒）
const POLLING_INTERVAL = 1000 // 轮询间隔（1秒）

// 计算倒计时显示
const countdownDisplay = computed(() => {
  if (countdown.value <= 0) return '00:00'
  const minutes = Math.floor(countdown.value / 60)
  const seconds = countdown.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 根据创建时间计算剩余时间
const calculateRemainingTime = (createTime: string) => {
  if (!createTime) return 0

  // 兼容iOS的日期格式处理
  let createDate: Date

  try {
    // 首先尝试直接解析
    createDate = new Date(createTime)

    // 检查是否为有效日期
    if (isNaN(createDate.getTime())) {
      // 如果直接解析失败，尝试转换格式
      // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
      const formattedString = createTime.replace(' ', 'T')
      createDate = new Date(formattedString)

      // 如果还是失败，尝试其他格式
      if (isNaN(createDate.getTime())) {
        // 尝试 "yyyy/MM/dd HH:mm:ss" 格式
        const slashFormatted = createTime.replace(/-/g, '/')
        createDate = new Date(slashFormatted)

        // 如果还是失败，返回0
        if (isNaN(createDate.getTime())) {
          console.warn('无法解析日期格式:', createTime)
          return 0
        }
      }
    }
  } catch (error) {
    console.error('日期解析错误:', error)
    return 0
  }

  const now = new Date()
  const timeDiff = now.getTime() - createDate.getTime()
  const remainingMs = 10 * 60 * 1000 - timeDiff // 10分钟减去已经过去的时间
  return Math.max(0, Math.floor(remainingMs / 1000))
}

// 开始倒计时
const startCountdown = () => {
  // 清除之前的定时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }

  if (!order.value?.createTime) return

  // 根据创建时间计算剩余时间
  const remainingSeconds = calculateRemainingTime(order.value.createTime)

  // 如果剩余时间小于等于0，不启动倒计时
  if (remainingSeconds <= 0) {
    countdown.value = 0
    return
  }

  countdown.value = remainingSeconds

  // 启动定时器
  countdownTimer.value = setInterval(() => {
    countdown.value--

    // 倒计时结束
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
      // 重新请求后端接口加载数据
      getMemberOrderByIdData()
      uni.showToast({ title: '支付超时，订单已自动取消', icon: 'none' })
    }
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
  pollingCount.value = 0
}

// 开始轮询查询秒杀订单
const startPollingSeckillOrder = () => {
  // 清除之前的轮询定时器
  stopPolling()

  // 重置轮询计数
  pollingCount.value = 0

  // 开始轮询
  pollingTimer.value = setInterval(async () => {
    try {
      pollingCount.value++

      // 构建查询参数 - 秒杀订单使用订单号查询
      const param = {
        opr: 'findById',
        orderSn: query.orderSn,
      }

      // 使用原有的API调用方式
      const res = await getMemberOrderByIdAPI(param as any)

      // 检查是否返回了数据
      if (res.code === 200 && res.result) {
        // 订单创建成功，停止轮询
        stopPolling()
        order.value = res.result

        // 如果是待付款状态，启动倒计时
        if (String(order.value.orderState) === '1') {
          startCountdown()
        }

        loading.value = false
        uni.showToast({ title: '订单创建成功', icon: 'success' })
        return
      }

      // 检查是否超过最大轮询次数
      if (pollingCount.value >= MAX_POLLING_COUNT) {
        stopPolling()
        loading.value = false
        uni.showToast({ title: '订单创建失败，请重试', icon: 'error' })
        // 返回上一页
        uni.navigateBack()
        return
      }
    } catch (error) {
      console.error('轮询查询订单失败:', error)
      pollingCount.value++

      // 检查是否超过最大轮询次数
      if (pollingCount.value >= MAX_POLLING_COUNT) {
        stopPolling()
        loading.value = false
        uni.showToast({ title: '订单创建失败，请重试', icon: 'error' })
        // 返回上一页
        uni.navigateBack()
        return
      }
    }
  }, POLLING_INTERVAL)
}

const getMemberOrderByIdData = async () => {
  try {
    loading.value = true

    // 根据订单类型构建查询参数
    let param: any = { opr: 'findById' }

    if (query.orderType === 'seckill' && query.orderSn) {
      // 秒杀订单使用订单号查询
      param.orderSn = query.orderSn
    } else if (query.id) {
      // 普通订单使用订单ID查询
      param.orderId = query.id
    } else {
      throw new Error('缺少必要的查询参数')
    }

    const res = await getMemberOrderByIdAPI(param)

    // 检查是否返回了数据
    if (res.code === 200 && res.result) {
      order.value = res.result

      // 如果是待付款状态，启动倒计时
      if (order.value && String(order.value.orderState) === '1') {
        startCountdown()
      } else {
        stopCountdown()
      }
    } else {
      // 如果是秒杀订单且没有返回数据，开始轮询
      if (query.orderType === 'seckill' && query.orderSn) {
        startPollingSeckillOrder()
        return // 不设置loading为false，保持加载状态
      } else {
        throw new Error('订单不存在')
      }
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    uni.showToast({ title: '获取订单详情失败', icon: 'error' })
  } finally {
    // 只有在非轮询状态下才设置loading为false
    if (!pollingTimer.value) {
      loading.value = false
    }
  }
}

onLoad(() => {
  getMemberOrderByIdData()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  stopCountdown()
  stopPolling()
})

// 复制内容
const onCopy = (text?: string) => {
  const copyText = text || order.value?.orderSn || ''
  uni.setClipboardData({
    data: copyText,
    success: () => {
      uni.showToast({ title: '复制成功', icon: 'success' })
    },
  })
}

// 订单状态映射
const ORDER_STATE_MAP: Record<string, { text: string; icon: string }> = {
  '1': { text: '等待付款', icon: 'icon-clock' },
  '2': { text: '待发货', icon: 'icon-package' },
  '3': { text: '待收货', icon: 'icon-delivery' },
  '4': { text: '待评价', icon: 'icon-comment' },
  '5': { text: '已完成', icon: 'icon-check' },
  '6': { text: '已取消', icon: 'icon-close' },
}

// 订单状态文本
const orderStateText = computed(() => {
  if (!order.value) return ''
  return ORDER_STATE_MAP[String(order.value.orderState)]?.text || '未知状态'
})

// 订单状态图标
const orderStateIcon = computed(() => {
  if (!order.value) return ''
  return ORDER_STATE_MAP[String(order.value.orderState)]?.icon || ''
})

// 支付订单
const onOrderPay = async (orderId: number) => {
  try {
    uni.showLoading({ title: '正在发起支付...' })

    // 构建支付参数 - 统一使用orderId
    const data = {
      opr: 'pay',
      userId: memberStore.profile.user.id,
      orderId: orderId,
    }

    // 调用支付接口
    const res = await getWxPayAPI(data)

    if (res.code !== 200) {
      uni.showToast({ title: res.msg || '支付失败', icon: 'error' })
      return
    }

    // 发起微信支付
    wx.requestPayment({
      timeStamp: res.data.timeStamp,
      nonceStr: res.data.nonceStr,
      package: res.data.package,
      signType: 'MD5',
      paySign: res.data.paySign,
      success: async () => {
        try {
          // 支付成功后，主动查询支付状态
          if (order.value?.orderSn) {
            const payStatusRes = await queryPayStatusAPI(order.value.orderSn)
            console.log('支付状态查询结果:', payStatusRes)
          }
        } catch (error) {
          console.error('查询支付状态失败:', error)
        }

        // 停止倒计时
        stopCountdown()

        uni.redirectTo({
          url: `/pagesOrder/payment/payment?id=${orderId}&orderType=${query.orderType || ''}`,
        })
      },
      fail: (err) => {
        console.log('支付失败', err)
        uni.showToast({ title: '支付已取消', icon: 'none' })
      },
    })
  } catch (error) {
    console.error('支付异常', error)
    uni.showToast({ title: '支付异常', icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

// 确认收货
const confirmReceipt = () => {
  uni.showModal({
    title: '提示',
    content: '确认已收到商品？',
    success: (res) => {
      if (res.confirm) {
        // 这里添加确认收货的API调用
        uni.showToast({ title: '确认收货成功', icon: 'success' })
        // 刷新订单状态
        getMemberOrderByIdData()
      }
    },
  })
}

// 取消订单
const cancelOrder = () => {
  uni.showModal({
    title: '提示',
    content: '确认取消订单？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 根据订单类型构建取消参数
          let param: any = { opr: 'cancelOrder' }

          if (query.orderType === 'seckill' && query.orderSn) {
            // 秒杀订单使用订单号
            param.orderSn = query.orderSn
          } else if (query.id) {
            // 普通订单使用订单ID
            param.orderId = query.id
          } else {
            throw new Error('缺少必要的订单参数')
          }

          const result = await cancelMemberOrderAPI(param)

          if (result.code !== 200) {
            uni.showToast({ title: result.msg || '取消订单失败', icon: 'error' })
            return
          }

          uni.showToast({ title: '订单已取消', icon: 'success' })
          getMemberOrderByIdData()
        } catch (error) {
          console.error('取消订单失败:', error)
          uni.showToast({ title: '取消订单失败', icon: 'error' })
        }
      }
    },
  })
}

// 删除订单
const deleteByIdOrder = () => {
  uni.showModal({
    title: '提示',
    content: '确认删除订单？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 根据订单类型构建删除参数
          let param: any = { opr: 'deleteOrder' }

          if (query.orderType === 'seckill' && query.orderSn) {
            // 秒杀订单使用订单号
            param.orderSn = query.orderSn
          } else if (query.id) {
            // 普通订单使用订单ID
            param.orderId = query.id
          } else {
            throw new Error('缺少必要的订单参数')
          }

          const result = await deleteByIdMemberOrderAPI(param)

          if (result.code !== 200) {
            uni.showToast({ title: result.msg || '删除订单失败', icon: 'error' })
            return
          }

          uni.showToast({ title: '删除订单成功', icon: 'success' })
          uni.switchTab({ url: '/pages/cart/cart' })
        } catch (error) {
          console.error('删除订单失败:', error)
          uni.showToast({ title: '删除订单失败', icon: 'error' })
        }
      }
    },
  })
}

// 计算总商品数量
const totalQuantity = computed(() => {
  if (!order.value?.goods) return 0
  return order.value.goods.reduce((sum, item) => sum + item.quantity, 0)
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''

  // 兼容iOS的日期格式处理
  let date: Date

  try {
    // 首先尝试直接解析
    date = new Date(dateString)

    // 检查是否为有效日期
    if (isNaN(date.getTime())) {
      // 如果直接解析失败，尝试转换格式
      // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
      const formattedString = dateString.replace(' ', 'T')
      date = new Date(formattedString)

      // 如果还是失败，尝试其他格式
      if (isNaN(date.getTime())) {
        // 尝试 "yyyy/MM/dd HH:mm:ss" 格式
        const slashFormatted = dateString.replace(/-/g, '/')
        date = new Date(slashFormatted)

        // 如果还是失败，返回原始字符串
        if (isNaN(date.getTime())) {
          console.warn('无法解析日期格式:', dateString)
          return dateString
        }
      }
    }
  } catch (error) {
    console.error('日期解析错误:', error)
    return dateString
  }

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
    date.getDate(),
  ).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(
    date.getMinutes(),
  ).padStart(2, '0')}`
}
</script>

<template>
  <scroll-view
    v-if="!loading"
    scroll-y
    class="viewport"
    id="scroller"
    @scrolltolower="onScrolltolower"
  >
    <template>
      <!-- 订单状态 -->
      <view class="overview" :style="{ paddingTop: safeAreaInsets!.top  + 'px' }">
        <view class="status" :class="orderStateIcon">{{ orderStateText }}</view>

        <!-- 待付款状态 -->
        <template v-if="String(order?.orderState) === '1'">
          <view class="tips">
            <text class="money">应付金额: ¥{{ order.actualPrice }}</text>
          </view>
          <view class="countdown" v-if="countdown > 0">
            <text class="countdown-text">支付剩余时间</text>
            <text class="countdown-time">{{ countdownDisplay }}</text>
          </view>
          <view class="button" @tap="onOrderPay(order.id)">去支付</view>
        </template>

        <!-- 待发货状态 -->
        <template v-else-if="String(order?.orderState) === '2'">
          <view class="tips">
            <text>商家正在备货中，请耐心等待</text>
          </view>
        </template>

        <!-- 待收货状态 -->
        <template v-else-if="String(order?.orderState) === '3'">
          <view class="tips">
            <text>商品已发货，请注意查收</text>
          </view>
          <view class="button" @tap="confirmReceipt">确认收货</view>
        </template>

        <!-- 其他状态 -->
        <template v-else>
          <view class="tips">
            <text>订单已{{ orderStateText }}</text>
          </view>
        </template>
      </view>
      <!-- 配送状态 -->
      <view class="shipment">
        <!-- 用户收货地址 -->
        <view class="locate" v-if="order?.address">
          <view class="user">{{ order.address.userName }} {{ order.address.telNumber }}</view>
          <view class="address">{{ order.address.fullLocation }} {{ order.address.detail }}</view>
        </view>

        <!-- 物流信息 -->
        <view class="logistics" v-if="order?.expressNumber">
          <view class="item">
            <text class="label">物流信息</text>
            <view class="value">
              {{ order.expressCompany || '暂无信息' }}
              <text v-if="order.expressNumber"> 单号：{{ order.expressNumber }}</text>
            </view>
            <text class="copy" v-if="order.expressNumber" @tap="onCopy(order.expressNumber)"
              >复制</text
            >
          </view>
        </view>
      </view>

      <!-- 商品信息 -->
      <view class="goods">
        <view class="item" v-for="(item, index) in order?.goods" :key="index">
          <navigator
            class="navigator"
            :url="`/pages/goods/goods?id=${item.goodsId}`"
            hover-class="none"
          >
            <image class="cover" :src="item.picture"></image>
            <view class="meta">
              <view class="name ellipsis">{{ item.name }}</view>
              <view class="type">{{ item.attrsText }}</view>
              <view class="price">
                <view class="actual">
                  <text class="symbol">¥</text>
                  <text>{{ item.curPrice }}</text>
                </view>
              </view>
              <view class="quantity">x{{ item.quantity }}</view>
            </view>
          </navigator>

          <!-- 待评价状态:展示按钮 -->
          <view class="action" v-if="String(order?.orderState) === '4'">
            <view class="button primary">申请售后</view>
            <navigator url="" class="button">去评价</navigator>
          </view>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="detail">
        <view class="title">订单信息</view>
        <view class="row">
          <text class="label">订单编号</text>
          <text class="value">{{ order?.orderSn }}</text>
          <text class="copy" @tap="onCopy(order?.orderSn)">复制</text>
        </view>
        <view class="row">
          <text class="label">下单时间</text>
          <text class="value">{{ formatDate(order?.createTime || '') }}</text>
        </view>
        <view class="row">
          <text class="label">支付方式</text>
          <text class="value">微信支付</text>
        </view>
      </view>

      <!-- 金额明细 -->
      <view class="detail">
        <view class="title">金额明细</view>
        <view class="row">
          <text class="label">商品总价</text>
          <text class="value">¥{{ order?.orderPrice }}</text>
        </view>
        <view class="row">
          <text class="label">运费</text>
          <text class="value">¥{{ order?.expressCost || '0.00' }}</text>
        </view>
        <view class="row">
          <text class="label">应付金额</text>
          <text class="value primary">¥{{ order?.actualPrice }}</text>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="toolbar-height" :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' }"></view>
    </template>
  </scroll-view>

  <!-- 加载中 -->
  <view v-else class="loading">
    <uni-load-more status="loading" />
  </view>

  <!-- 底部操作栏 -->
  <view class="toolbar" v-if="order">
    <!-- 待付款状态:展示支付按钮 -->
    <template v-if="String(order.orderState) === '1'">
      <view class="button primary" @tap="onOrderPay(order.id)">去支付</view>
      <view class="button" @tap="cancelOrder">取消订单</view>
    </template>

    <!-- 待收货状态:展示确认收货按钮 -->
    <template v-else-if="String(order.orderState) === '3'">
      <view class="button primary" @tap="confirmReceipt">确认收货</view>
      <view class="button">查看物流</view>
    </template>

    <!-- 待评价状态:展示去评价按钮 -->
    <template v-else-if="String(order.orderState) === '4'">
      <navigator url="" class="button primary">去评价</navigator>
      <view class="button">申请售后</view>
    </template>

    <!-- 已完成状态:展示再次购买按钮 -->
    <template v-else-if="String(order.orderState) === '5'">
      <view class="button primary">再次购买</view>
      <view class="button">申请售后</view>
    </template>

    <!-- 已取消状态:展示删除订单按钮 -->
    <template v-else-if="String(order.orderState) === '6'">
      <view class="button delete" @tap="deleteByIdOrder">删除订单</view>
    </template>
  </view>
</template>

<style lang="scss">
page {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: #f4f4f4;
}

.navbar {
  width: 100%;
  color: #000;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  background-color: transparent;

  .wrap {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #fff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .back {
      position: absolute;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 60rpx;
      height: 60rpx;
      font-size: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      backdrop-filter: blur(10px);
    }
  }
}

.viewport {
  background-color: #f7f7f8;
}

.overview {
  display: flex;
  flex-direction: column;
  align-items: center;

  line-height: 1;
  padding-bottom: 30rpx;
  color: #fff;
  // background-image: url(https://pcapi-xiaotuxian-front-devtest.itheima.net/miniapp/images/order_bg.png);
  background-color: #3c69dc;
  background-size: cover;

  .status {
    font-size: 36rpx;
  }

  .status::before {
    margin-right: 6rpx;
    font-weight: 500;
  }

  .tips {
    margin: 30rpx 0;
    display: flex;
    font-size: 14px;
    align-items: center;

    .money {
      // margin-right: 30rpx;
    }
  }

  .countdown {
    margin: 20rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .countdown-text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 10rpx;
    }

    .countdown-time {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
      font-family: 'Courier New', monospace;
    }
  }

  .button-group {
    margin-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .button {
    width: 260rpx;
    height: 64rpx;
    margin: 0 10rpx;
    text-align: center;
    line-height: 64rpx;
    font-size: 28rpx;
    color: #3c69dc;
    border-radius: 68rpx;
    background-color: #fff;
  }
}

.shipment {
  line-height: 1.4;
  padding: 0 20rpx;
  margin: 20rpx 20rpx 0;
  border-radius: 10rpx;
  background-color: #fff;

  .locate,
  .item {
    min-height: 120rpx;
    padding: 30rpx 30rpx 25rpx 75rpx;
    background-size: 50rpx;
    background-repeat: no-repeat;
    background-position: 6rpx center;
  }

  .locate {
    background-image: url(https://pcapi-xiaotuxian-front-devtest.itheima.net/miniapp/images/locate.png);

    .user {
      font-size: 26rpx;
      color: #444;
    }

    .address {
      font-size: 24rpx;
      color: #666;
    }
  }

  .item {
    background-image: url(https://pcapi-xiaotuxian-front-devtest.itheima.net/miniapp/images/car.png);
    border-bottom: 1rpx solid #eee;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      font-size: 26rpx;
      color: #444;
      margin-right: 20rpx;
    }

    .value {
      font-size: 24rpx;
      color: #666;
      flex: 1;
    }

    .copy {
      border-radius: 20rpx;
      font-size: 20rpx;
      border: 1px solid #ccc;
      padding: 5rpx 10rpx;
      margin-left: 10rpx;
      flex-shrink: 0;
    }
  }
}

.goods {
  margin: 20rpx 20rpx 0;
  padding: 0 20rpx;
  border-radius: 10rpx;
  background-color: #fff;

  .item {
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eee;

    .navigator {
      display: flex;
      margin: 20rpx 0;
    }

    .cover {
      width: 170rpx;
      height: 170rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
    }

    .meta {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
    }

    .name {
      height: 80rpx;
      font-size: 26rpx;
      color: #444;
    }

    .type {
      line-height: 1.8;
      padding: 0 15rpx;
      margin-top: 6rpx;
      font-size: 24rpx;
      align-self: flex-start;
      border-radius: 4rpx;
      color: #888;
      background-color: #f7f7f8;
    }

    .price {
      display: flex;
      margin-top: 6rpx;
      font-size: 24rpx;
    }

    .symbol {
      font-size: 20rpx;
    }

    .original {
      color: #999;
      text-decoration: line-through;
    }

    .actual {
      margin-left: 10rpx;
      color: #444;
    }

    .text {
      font-size: 22rpx;
    }

    .quantity {
      position: absolute;
      bottom: 0;
      right: 0;
      font-size: 24rpx;
      color: #444;
    }

    .action {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-start;
      padding: 30rpx 0 0;

      .button {
        width: 200rpx;
        height: 60rpx;
        text-align: center;
        justify-content: center;
        line-height: 60rpx;
        margin-left: 20rpx;
        border-radius: 60rpx;
        border: 1rpx solid #ccc;
        font-size: 26rpx;
        color: #444;
      }

      .primary {
        color: #3c69dc;
        border-color: #3c69dc;
      }
    }
  }

  .total {
    line-height: 1;
    font-size: 26rpx;
    padding: 20rpx 0;
    color: #666;

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10rpx 0;
    }

    .symbol::before {
      content: '¥';
      font-size: 80%;
      margin-right: 3rpx;
    }

    .primary {
      color: #cf4444;
      font-size: 36rpx;
    }
  }
}

.detail {
  line-height: 1;
  padding: 30rpx 20rpx 0;
  margin: 20rpx 20rpx 0;
  font-size: 26rpx;
  color: #666;
  border-radius: 10rpx;
  background-color: #fff;

  .title {
    font-size: 30rpx;
    color: #444;
  }

  .row {
    padding: 20rpx 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      flex-shrink: 0;
    }

    .value {
      flex: 1;
      text-align: right;
      margin-right: 10rpx;

      &.primary {
        color: #cf4444;
        font-size: 30rpx;
      }
    }

    .copy {
      border-radius: 20rpx;
      font-size: 20rpx;
      border: 1px solid #ccc;
      padding: 5rpx 10rpx;
      flex-shrink: 0;
    }
  }
}

.guess {
  margin: 20rpx;
  padding: 30rpx 30rpx 0;
  background-color: #fff;
  border-radius: 10rpx;

  .title {
    font-size: 30rpx;
    margin-bottom: 20rpx;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.toolbar-height {
  height: 100rpx;
  box-sizing: content-box;
}

.toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: calc(var(--window-bottom));
  z-index: 1;

  height: 100rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  border-top: 1rpx solid #ededed;
  border-bottom: 1rpx solid #ededed;
  background-color: #fff;
  box-sizing: content-box;

  .button {
    display: flex;
    justify-content: center;
    align-items: center;

    width: 200rpx;
    height: 72rpx;
    margin-left: 15rpx;
    font-size: 26rpx;
    border-radius: 72rpx;
    border: 1rpx solid #ccc;
    color: #444;
  }

  .delete {
    order: 4;
  }

  .button {
    order: 3;
  }

  .secondary {
    order: 2;
    color: #3c69dc;
    border-color: #3c69dc;
  }

  .primary {
    order: 1;
    color: #fff;
    background-color: #3c69dc;
  }
}

.popup-root {
  padding: 30rpx 30rpx 0;
  border-radius: 10rpx 10rpx 0 0;
  overflow: hidden;

  .title {
    font-size: 30rpx;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .description {
    font-size: 28rpx;
    padding: 0 20rpx;

    .tips {
      color: #444;
      margin-bottom: 12rpx;
    }

    .cell {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15rpx 0;
      color: #666;
    }

    .icon::before {
      content: '\e6cd';
      font-family: 'erabbit' !important;
      font-size: 38rpx;
      color: #999;
    }

    .icon.checked::before {
      content: '\e6cc';
      font-size: 38rpx;
      color: #3c69dc;
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 0 40rpx;
    font-size: 28rpx;
    color: #444;

    .button {
      flex: 1;
      height: 72rpx;
      text-align: center;
      line-height: 72rpx;
      margin: 0 20rpx;
      color: #444;
      border-radius: 72rpx;
      border: 1rpx solid #ccc;
    }

    .primary {
      color: #fff;
      background-color: #3c69dc;
      border: none;
    }
  }
}

// 图标样式
.icon-clock::before {
  // content: '\e74f';
  font-family: 'erabbit' !important;
}

.icon-package::before {
  content: '\e7b9';
  font-family: 'erabbit' !important;
}

.icon-delivery::before {
  content: '\e7df';
  font-family: 'erabbit' !important;
}

.icon-comment::before {
  content: '\e6bf';
  font-family: 'erabbit' !important;
}

.icon-check::before {
  content: '\e6cc';
  font-family: 'erabbit' !important;
}

.icon-close::before {
  content: '\e6cd';
  font-family: 'erabbit' !important;
}
</style>
