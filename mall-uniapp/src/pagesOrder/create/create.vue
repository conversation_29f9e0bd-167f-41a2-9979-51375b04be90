<script setup lang="ts">
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { computed, ref, nextTick } from 'vue'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 从src/stores中导入useMemberStore，用于获取登录的用户信息
import type { CartItem } from '@/types/cart'
// 导入useAddressStore，用于获取登录用户默认的收货地址
import { useAddressStore } from '@/stores/modules/address'
import { postMemberOrderAPI } from '@/services/order'
// 移除未实现的秒杀订单API导入
import { useMemberStore } from '@/stores'
// 导入秒杀订单API
import { createSeckillOrder, generateSeckillUrl } from '@/services/seckill/order'
// 导入滑块验证组件
import SlideVerify from '@/components/SlideVerify.vue'
// 导入优惠券API
import { getMemberCouponsAPI, type CouponItem } from '@/services/coupon'

// 调用useAddressStore()事件，将返回此事件中返回的值赋值给addressStore
const addressStore = useAddressStore()
// 获取登录用户默认的收货地址
const selecteAddress = computed(() => {
  return addressStore.selectedAddress
})

// 用来保存从cart.vue传递过来的用户选中的购物车列表
const cartList = ref<CartItem[]>([])
const totalPrice = ref(0.0) // 用来保存从cart.vue传递过来的商品总价
let postFee = 10.0 // 运费
// 订单类型：普通订单或秒杀订单
const orderType = ref('normal')
// 优惠券相关
const selectedCouponId = ref<number | null>(null)
const availableCoupons = ref<CouponItem[]>([])
const couponDiscount = ref(0.0) // 优惠券减免金额
const selectedCoupon = ref<CouponItem | null>(null)
const showCouponPopup = ref(false)
const couponPopupRef = ref()

// 滑块验证相关
const showSliderVerify = ref(false)
const sliderVerified = ref(false)
const sliderLoading = ref(false)
const sliderPopupRef = ref()
const slideVerifyRef = ref()

onLoad((options) => {
  console.log('订单创建页接收到的参数:', options)
  // 商品总价，是从cart.vue购物车页面传递过来的
  const priceFromOptions = options?.totalPrice
  console.log('接收到的totalPrice:', priceFromOptions, typeof priceFromOptions)
  
  // 如果没有传递totalPrice，从cartList计算
  if (!priceFromOptions || priceFromOptions === 'undefined' || isNaN(Number(priceFromOptions))) {
    // cartList是从cart.vue传递过来的用户选中的购物车列表
    cartList.value = JSON.parse(options?.cartList || '[]')
    // 从购物车商品计算总价
    const calculatedPrice = cartList.value.reduce((total, item) => {
      return total + (Number(item.nowPrice) * Number(item.count))
    }, 0)
    totalPrice.value = calculatedPrice
    console.log('从购物车计算的总价:', calculatedPrice)
  } else {
    totalPrice.value = Number(priceFromOptions)
    cartList.value = JSON.parse(options?.cartList || '[]')
  }
  
  console.log('最终设置的totalPrice.value:', totalPrice.value)
  // 获取订单类型，区分普通订单和秒杀订单
  if (options?.orderType) {
    // 直接赋值并转为小写，以防大小写不一致
    orderType.value = String(options.orderType).trim().toLowerCase()
  }

  // 检查商品中是否包含秒杀标记
  const hasSeckillItem = cartList.value.some((item) => item.activityType === 'seckill')

  // 如果参数或商品标记了秒杀，则设置为秒杀订单
  if (orderType.value === 'seckill' || hasSeckillItem) {
    orderType.value = 'seckill'
    console.log('确认为秒杀订单')
  }
  
  // 获取可用优惠券
  loadAvailableCoupons()
  
  // 监听优惠券选择事件
  uni.$on('couponSelected', (couponData) => {
    selectedCoupon.value = {
      id: couponData.id,
      name: couponData.name,
      discount: couponData.discount,
      minAmount: couponData.minAmount,
      type: 'fixed'
    }
    selectedCouponId.value = couponData.id
    couponDiscount.value = couponData.discount
    
    uni.showToast({
      title: '优惠券选择成功',
      icon: 'success'
  })
})

// 页面卸载时清理事件监听
onUnload(() => {
  uni.$off('couponSelected')
})
})

// 获取可用优惠券列表
const loadAvailableCoupons = async () => {
  try {
    const orderAmount = Number(totalPrice.value)
    const res = await getMemberCouponsAPI(memberStore.profile.user.id, orderAmount)
    if (res.code === 200) {
      // 修正数据获取路径，从res.rows获取优惠券数据
      const coupons = res.rows || []
      // 转换数据格式以匹配前端组件需要的格式
      availableCoupons.value = coupons.map(item => ({
        id: item.id,
        name: item.coupon?.couponName || '优惠券',
        discount: item.coupon?.faceValue || 0,
        minAmount: item.coupon?.minSpend || 0,
        type: item.coupon?.couponType === 2 ? 'percent' : 'fixed',
        description: item.coupon?.remark || '',
        startTime: item.coupon?.startTime || '',
        endTime: item.coupon?.endTime || '',
        maxDiscount: item.coupon?.maxDiscount || null
      }))
    }
  } catch (error) {
    console.error('获取优惠券失败:', error)
  }
}

// 选择优惠券
const selectCoupon = (coupon: CouponItem) => {
  selectedCoupon.value = coupon
  selectedCouponId.value = coupon.id
  
  // 计算优惠金额
  const orderAmount = Number(totalPrice.value)
  if (coupon.type === 'fixed') {
    couponDiscount.value = coupon.discount
  } else {
    const discount = orderAmount * (coupon.discount / 100)
    couponDiscount.value = coupon.maxDiscount ? Math.min(discount, coupon.maxDiscount) : discount
  }
  
  showCouponPopup.value = false
  uni.showToast({ title: '优惠券选择成功', icon: 'success' })
}

// 取消选择优惠券
const cancelCoupon = () => {
  selectedCoupon.value = null
  selectedCouponId.value = null
  couponDiscount.value = 0
  showCouponPopup.value = false
}

// 打开优惠券选择弹窗
const openCouponPopup = () => {
  showCouponPopup.value = true
}

// 跳转到优惠券页面
const goToCouponCenter = () => {
  // 获取所有商品的ID和分类ID
  const goodsIds = cartList.value.map(item => item.goodsId).filter(id => id)
  const categoryIds = cartList.value.map(item => item.categoryId).filter(id => id)
  
  let url = '/pagesMember/coupon/coupon?from=order&status=0'
  
  // 添加商品ID参数（多个商品用逗号分隔）
  if (goodsIds.length > 0) {
    url += `&goodsIds=${goodsIds.join(',')}`
  }
  
  // 添加商品分类ID参数（多个分类用逗号分隔，去重）
  if (categoryIds.length > 0) {
    const uniqueCategoryIds = [...new Set(categoryIds)]
    url += `&categoryIds=${uniqueCategoryIds.join(',')}`
  }
  
  console.log('跳转到优惠券页面，URL:', url)
  console.log('传递的商品IDs:', goodsIds)
  console.log('传递的分类IDs:', categoryIds)
  
  uni.navigateTo({
    url: url
  })
}

// 计算最终支付金额
const finalAmount = computed(() => {
  const goodsPrice = Number(totalPrice.value) || 0
  const shipping = Number(postFee) || 0
  const discount = Number(couponDiscount.value) || 0
  return Math.max(0, goodsPrice + shipping - discount)
})

// 导入deleteMemberCartAPI事件，下单成功后，将此订单所对应的商品信息从购物车中删除
import { deleteMemberCartAPI } from '@/services/cart'
// 获取会员Store
const memberStore = useMemberStore()

// 生成模拟订单ID
const generateOrderId = () => {
  return Date.now().toString() + Math.floor(Math.random() * 1000)
}

// 滑块验证成功回调
const onSliderSuccess = () => {
  sliderVerified.value = true
  showSliderVerify.value = false
  // 验证成功后直接提交订单
  submitOrder()
}

// 滑块验证失败回调
const onSliderFail = () => {
  sliderVerified.value = false
  showSliderVerify.value = false
  uni.showToast({
    title: '验证失败，请重试',
    icon: 'none',
  })
}

// 滑块验证关闭回调
const onSliderClose = () => {
  showSliderVerify.value = false
}

// 提交订单（实际的下单逻辑）
const submitOrder = async () => {
  // 没有收货地址提醒
  if (!selecteAddress.value?.id) {
    return uni.showToast({ icon: 'none', title: '请选择收货地址' })
  }

  // 再次检查是否为秒杀订单类型
  let isSeckillOrder = orderType.value === 'seckill'

  try {
    let res

    if (isSeckillOrder) {
      // 获取秒杀商品信息
      const seckillItem = cartList.value[0]

      // 构建秒杀订单数据
      const seckillOrderData = {
        userId: memberStore.profile.user.id,
        activityId: seckillItem.activityId || 0,
        goodsId: seckillItem.goodsId,
        secKillGoodsId: seckillItem.secKillGoodsId || 0,
        skuId: seckillItem.skuId,
        buyCount: seckillItem.count,
        secKillPrice: seckillItem.nowPrice,
        totalAmount: Number(totalPrice.value) + postFee,
        addressId: selecteAddress.value?.id,
        attrsText: seckillItem.attrsText,
      }

      // 第一步：生成秒杀地址
      const urlRes = await generateSeckillUrl(seckillOrderData)
      if (urlRes.code !== 200) {
        uni.showToast({ icon: 'error', title: urlRes.msg || '获取秒杀地址失败' })
        return
      }

      const seckillUrl = urlRes.data
      console.log('获取到秒杀地址:', seckillUrl)

      // 第二步：使用动态地址进行秒杀下单
      res = await createSeckillOrder(seckillOrderData, seckillUrl)
    } else {
      // 创建普通订单对象
      const order = {
        userId: memberStore.profile.user.id,
        orderPrice: totalPrice.value,
        actualPrice: finalAmount.value,
        addressId: selecteAddress.value?.id,
        couponId: selectedCouponId.value,
        couponDiscount: couponDiscount.value,
        goods: cartList.value.map((v) => ({
          skuId: v.skuId,
          goodsId: v.goodsId,
          name: v.name,
          attrsText: v.attrsText,
          quantity: v.count,
        })),
      }

      // 调用普通订单API
      res = await postMemberOrderAPI(order)
    }

    if (res.code === 200) {
      uni.showToast({ icon: 'success', title: '下单成功!' })
      // 如果是普通购物车商品，需要从购物车删除
      if (!isSeckillOrder) {
        let ids = cartList.value.map((cart) => cart.id).filter((id) => id)
        if (ids.length > 0) {
          await deleteMemberCartAPI(ids)
        }
      }

      // 处理秒杀订单返回的订单号
      console.log('订单创建响应:', res)

      if (isSeckillOrder) {
        // 秒杀订单返回的是订单号orderSn
        const orderSn = res.data?.orderSn
        if (orderSn) {
          uni.redirectTo({
            url: `/pagesOrder/detail/detail?orderSn=${orderSn}&orderType=seckill`,
          })
        } else {
          uni.showToast({ icon: 'error', title: '订单创建失败，未获取到订单号' })
        }
      } else {
        // 普通订单返回的是订单ID
        const orderId = res.result?.id
        if (orderId) {
          uni.redirectTo({
            url: `/pagesOrder/detail/detail?id=${orderId}&orderType=${orderType.value}`,
          })
        } else {
          uni.showToast({ icon: 'error', title: '订单创建失败，未获取到订单ID' })
        }
      }
    } else {
      uni.showToast({ icon: 'error', title: res.msg || '下单失败!' })
    }
  } catch (error) {
    console.error('下单失败:', error)
    uni.showToast({ icon: 'error', title: '网络异常，请稍后再试' })
  }
}

// 提交订单（带验证）
const onOrderSubmit = () => {
  // 如果是秒杀订单，需要先进行滑块验证
  if (orderType.value === 'seckill') {
    console.log('显示滑块验证弹窗')
    // 使用uni.showModal作为临时解决方案
    uni.showModal({
      title: '安全验证',
      content: '为了确保秒杀安全，请完成滑块验证',
      showCancel: false,
      confirmText: '开始验证',
      success: (res) => {
        if (res.confirm) {
          // 显示滑块验证弹窗
          showSliderVerify.value = true
          // 确保弹窗显示
          nextTick(() => {
            if (sliderPopupRef.value) {
              sliderPopupRef.value.open('center')
            }
          })
        }
      },
    })
  } else {
    // 普通订单直接提交
    submitOrder()
  }
}
</script>

<template>
  <scroll-view scroll-y class="viewport">
    <!-- 收货地址 -->
    <navigator
      v-if="selecteAddress"
      class="shipment"
      hover-class="none"
      url="/pagesMember/address/address?from=order"
    >
      <view class="user"> {{ selecteAddress?.userName }} {{ selecteAddress?.telNumber }} </view>
      <view class="address"> {{ selecteAddress?.fullLocation }} {{ selecteAddress?.detail }} </view>
      <text class="icon icon-right"></text>
    </navigator>
    <navigator
      v-else
      class="shipment"
      hover-class="none"
      url="/pagesMember/address/address?from=order"
    >
      <view class="address"> 请选择收货地址 </view>
      <text class="icon icon-right"></text>
    </navigator>

    <!-- 商品信息 -->
    <view class="goods">
      <navigator
        v-for="item in cartList"
        :key="item.id"
        :url="`/pages/goods/goods?id=${item.goodsId}`"
        class="item"
        hover-class="none"
      >
        <image class="picture" :src="item.picture" />
        <view class="meta">
          <view class="name ellipsis"> {{ item.name }} </view>
          <view class="attrs">{{ item.attrsText }}</view>
          <view class="prices">
            <view class="pay-price symbol">{{ item.nowPrice }}</view>
            <view class="price symbol">{{ item.price }}</view>
          </view>
          <view class="count">x{{ item.count }}</view>
        </view>
      </navigator>
    </view>

    <!-- 优惠券选择 -->
    <view class="related">
      <view class="item arrow" @tap="openCouponPopup">
        <text class="text">优惠券</text>
        <view class="coupon-info">
          <text v-if="selectedCoupon" class="coupon-selected">
            {{ selectedCoupon.name }} -¥{{ couponDiscount.toFixed(2) }}
          </text>
          <text v-else class="coupon-placeholder">
            {{ availableCoupons.length > 0 ? `${availableCoupons.length}张可用` : '暂无可用' }}
          </text>
        </view>
      </view>
      <view class="item arrow" @tap="goToCouponCenter">
        <text class="text">我的优惠券</text>
        <text class="coupon-center-text">去券中心</text>
      </view>
    </view>

    <!-- 支付金额 -->
    <view class="settlement">
      <view class="item">
        <text class="text">商品总价: </text>
        <text class="number symbol">{{finalAmount.toFixed(2) }}</text>
      </view>
      <view class="item">
        <text class="text">运费: </text>
        <text class="number symbol">{{ postFee }}</text>
      </view>
      <view class="item" v-if="couponDiscount > 0">
        <text class="text">优惠券: </text>
        <text class="number danger">-¥{{ couponDiscount.toFixed(2) }}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 吸底工具栏 -->
  <view class="toolbar" :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' }">
    <view class="total-pay symbol">
      <text class="number">{{ finalAmount.toFixed(2) }}</text>
    </view>
    <view class="button" :class="{ disabled: !selecteAddress?.id }" @tap="onOrderSubmit"> 提交订单 </view>
  </view>

  <!-- 优惠券选择弹窗 -->
  <uni-popup ref="couponPopupRef" type="bottom" v-model:show="showCouponPopup">
    <view class="coupon-popup">
      <view class="coupon-header">
        <text class="coupon-title">选择优惠券</text>
        <text class="coupon-close" @tap="showCouponPopup = false">×</text>
      </view>
      <scroll-view scroll-y class="coupon-list">
        <view class="coupon-item" v-for="coupon in availableCoupons" :key="coupon.id">
          <view class="coupon-content" @tap="selectCoupon(coupon)">
            <view class="coupon-left">
              <view class="coupon-amount">
                <text class="coupon-symbol">¥</text>
                <text class="coupon-value">{{ coupon.discount }}</text>
                <text class="coupon-unit" v-if="coupon.type === 'percent'">%</text>
              </view>
              <view class="coupon-condition">满{{ coupon.minAmount }}元可用</view>
            </view>
            <view class="coupon-right">
              <view class="coupon-name">{{ coupon.name }}</view>
              <view class="coupon-desc">{{ coupon.description }}</view>
              <view class="coupon-time">{{ coupon.startTime }} - {{ coupon.endTime }}</view>
            </view>
            <view class="coupon-select" :class="{ active: selectedCouponId === coupon.id }">
              <text class="coupon-check">✓</text>
            </view>
          </view>
        </view>
        <view class="coupon-item" v-if="selectedCoupon">
          <view class="coupon-content no-coupon" @tap="cancelCoupon">
            <view class="coupon-left">
              <view class="coupon-amount">
                <text class="coupon-symbol">不</text>
                <text class="coupon-value">使用</text>
              </view>
            </view>
            <view class="coupon-right">
              <view class="coupon-name">不使用优惠券</view>
            </view>
            <view class="coupon-select" :class="{ active: !selectedCouponId }">
              <text class="coupon-check">✓</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>

  <!-- 滑块验证弹窗 -->
  <uni-popup ref="sliderPopupRef" type="center" @close="onSliderClose">
    <view class="slider-verify-container">
      <view class="slider-verify-header">
        <text class="slider-verify-title">安全验证</text>
        <text class="slider-verify-subtitle">请完成滑块验证以继续下单</text>
      </view>
      <view class="slider-verify-content">
        <SlideVerify ref="slideVerifyRef" @success="onSliderSuccess" @failed="onSliderFail" />
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss">
page {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: #f4f4f4;
}

.symbol::before {
  content: '¥';
  font-size: 80%;
  margin-right: 5rpx;
}

.shipment {
  margin: 20rpx;
  padding: 30rpx 30rpx 30rpx 84rpx;
  font-size: 26rpx;
  border-radius: 10rpx;
  background: url(https://pcapi-xiaotuxian-front-devtest.itheima.net/miniapp/images/locate.png)
    20rpx center / 50rpx no-repeat #fff;
  position: relative;

  .icon {
    font-size: 36rpx;
    color: #333;
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 20rpx;
  }

  .user {
    color: #333;
    margin-bottom: 5rpx;
  }

  .address {
    color: #666;
  }
}

.goods {
  margin: 20rpx;
  padding: 0 20rpx;
  border-radius: 10rpx;
  background-color: #fff;

  .item {
    display: flex;
    padding: 30rpx 0;
    border-top: 1rpx solid #eee;

    &:first-child {
      border-top: none;
    }

    .picture {
      width: 170rpx;
      height: 170rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
    }

    .meta {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
    }

    .name {
      height: 80rpx;
      font-size: 26rpx;
      color: #444;
    }

    .attrs {
        line-height: 1.8;
        padding: 0 15rpx;
        margin-top: 6rpx;
        font-size: 24rpx;
        align-self: flex-start;
        border-radius: 4rpx;
        color: #888;
        background-color: #f7f7f8;
        
        max-width: 100%; /* 限制最大宽度为父容器宽度 */
        display: -webkit-box; /* 弹性盒模型 */
        -webkit-line-clamp: 2; /* 最多显示2行 */
        -webkit-box-orient: vertical; /* 垂直排列 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }

    .prices {
      display: flex;
      align-items: baseline;
      margin-top: 6rpx;
      font-size: 28rpx;

      .pay-price {
        margin-right: 10rpx;
        color: #cf4444;
      }

      .price {
        font-size: 24rpx;
        color: #999;
        text-decoration: line-through;
      }
    }

    .count {
      position: absolute;
      bottom: 0;
      right: 0;
      font-size: 26rpx;
      color: #444;
    }
  }
}

.related {
  margin: 20rpx;
  padding: 0 20rpx;
  border-radius: 10rpx;
  background-color: #fff;

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 80rpx;
    font-size: 26rpx;
    color: #333;
  }

  .input {
    flex: 1;
    text-align: right;
    margin: 20rpx 0;
    padding-right: 20rpx;
    font-size: 26rpx;
    color: #999;
  }

  .item .text {
    width: 125rpx;
  }

  .picker {
    color: #666;
  }

  .picker::after {
    content: '\e6c2';
  }
}

/* 结算清单 */
.settlement {
  margin: 20rpx;
  padding: 0 20rpx;
  border-radius: 10rpx;
  background-color: #fff;

  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    font-size: 26rpx;
    color: #333;
  }

  .danger {
    color: #cf4444;
  }
}

/* 吸底工具栏 */
.toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: calc(var(--window-bottom));
  z-index: 1;

  background-color: #fff;
  height: 100rpx;
  padding: 0 20rpx;
  border-top: 1rpx solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: content-box;

  .total-pay {
    font-size: 40rpx;
    color: #cf4444;

    .decimal {
      font-size: 75%;
    }
  }

  .button {
    width: 220rpx;
    text-align: center;
    line-height: 72rpx;
    font-size: 26rpx;
    color: #fff;
    border-radius: 72rpx;
    background-color: #3c69dc;
  }

  .disabled {
    opacity: 0.6;
  }
}

/* 滑块验证样式 */
.slider-verify-container {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
}

.slider-verify-header {
  margin-bottom: 40rpx;
}

.slider-verify-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.slider-verify-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.slider-verify-content {
  margin-bottom: 40rpx;
}

/* 优惠券相关样式 */
.coupon-info {
  display: flex;
  align-items: center;
}

.coupon-selected {
  color: #cf4444;
  font-size: 26rpx;
}

.coupon-placeholder {
  color: #999;
  font-size: 26rpx;
}

.coupon-center-text {
  color: #3c69dc;
  font-size: 26rpx;
}

.coupon-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.coupon-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.coupon-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.coupon-list {
  flex: 1;
  padding: 20rpx;
}

.coupon-item {
  margin-bottom: 20rpx;
}

.coupon-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  position: relative;
}

.coupon-left {
  width: 120rpx;
  text-align: center;
  border-right: 2rpx dashed #eee;
  padding-right: 20rpx;
  margin-right: 20rpx;
}

.coupon-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 10rpx;
}

.coupon-symbol {
  font-size: 24rpx;
  color: #cf4444;
}

.coupon-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #cf4444;
}

.coupon-unit {
  font-size: 24rpx;
  color: #cf4444;
}

.coupon-condition {
  font-size: 20rpx;
  color: #999;
}

.coupon-right {
  flex: 1;
}

.coupon-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coupon-time {
  font-size: 20rpx;
  color: #999;
}

.coupon-select {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.coupon-select.active {
  background-color: #3c69dc;
  border-color: #3c69dc;
}

.coupon-select.active .coupon-check {
  color: #fff;
  font-size: 24rpx;
}

.coupon-check {
  color: transparent;
  font-size: 24rpx;
}

.no-coupon .coupon-left {
  border-right: none;
}

.no-coupon .coupon-amount {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 10rpx;
}

.no-coupon .coupon-symbol,
.no-coupon .coupon-value {
  color: #999;
  font-size: 28rpx;
}
</style>
