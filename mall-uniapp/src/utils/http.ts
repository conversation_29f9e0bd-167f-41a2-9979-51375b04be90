/**
 * 添加拦截器:
 *   拦截 request 请求
 *   拦截 uploadFile 文件上传
 * TODO:
 *   1. 非 http 开头需拼接地址
 *   2. 请求超时
 *   3. 添加小程序端请求头标识
 *   4. 添加 token 请求头标识
 */
import { useMemberStore } from '@/stores'

// 下面baseUrl为后台的请求地址
const baseURL = 'http://localhost:18080'

// 添加拦截器(用于拦截前端的所有请求)
const httpInterceptor = {
  // 拦截前触发  其中UniApp.RequestOptions为当前请求对象
  invoke(options: UniApp.RequestOptions) {
    // 判断是否为第三方请求（如腾讯地图API）
    const isThirdPartyRequest =
      options.url.includes('apis.map.qq.com') ||
      options.url.includes('https://') ||
      options.url.includes('http://')

    // 1. 拼接前端请求地址，只有非第三方请求才拼接baseURL
    if (!isThirdPartyRequest) {
      options.url = baseURL + options.url
    }

    // 2. 请求超时为60秒
    options.timeout = 60 * 1000
    // 3. 添加小程序端请求头标识
    options.header = {
      // ...options.header得到uni.request({ header:{} })中添加的请求头标识。
      // … 为拓展运算符，用于取出参数对象所有可遍历属性。
      ...options.header,
      // 在原有请求头标识的基础上，再添加source-client标识(本次请求的来源是miniapp)。
      'source-client': 'miniapp',
    }
    // 4. 添加 token 请求头标识（只有非第三方请求才添加token）
    if (!isThirdPartyRequest) {
      // memberStore用来接收调用useMemberStore()事件返回的值
      const memberStore = useMemberStore()
      // profile后面的?是什么意思呢？用来判断如果profile对象为undefined时，在profile后面的?起到的作用是不会执行.token代码。
      // 从pinia中取出profile对象中的token值。
      const token = memberStore.profile?.token
      if (token) {
        // 将token添加到请求头中，请求参数名为Authorization
        options.header.Authorization = token
      }
    }
  },
}
// 添加请求拦截器，用于拦截所有的request请求
uni.addInterceptor('request', httpInterceptor)
// 添加文件上传请求拦截器，用于拦截所有的uploadFile文件上传的请求。
// uni.addInterceptor('uploadFile', httpInterceptor)

/**
 * 请求函数
 * @param  UniApp.RequestOptions
 * @returns Promise
 *  1. 返回 Promise 对象
 *  2. 获取数据成功
 *    2.1 提取核心数据 res.data
 *    2.2 添加类型，支持泛型
 *  3. 获取数据失败
 *    3.1 401错误  -> 清理用户信息，跳转到登录页
 *    3.2 其他错误 -> 根据后端错误信息轻提示
 *    3.3 网络错误 -> 提示用户换网络
 */
// 在TypeScrip中interface表示接口，与Java中接口概念类似。它定义了对象具有的属性。
// 下面定义接口，用于接收从后端响应过来的数据
interface Data<T> {
  // Data为接口名，<T>为此接口的泛型，其中T表示任意类型。
  code: number // 状态码，常见的状态友有：200、404、500
  msg: string // 响应的消息
  result: T // 消息结果
}
// 2.2 添加类型，支持泛型
export const http = <T>(options: UniApp.RequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<Data<T>>((resolve, reject) => {
    uni.request({
      // 发异步请求时，会被拦截器拦截。
      ...options, // 解构出options中的url、method、data等请求参数
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 检查响应数据中的 code
          const data = res.data as Data<T>
          if (data.code === 401) {
            // 处理 code 401 的情况
            const memberStore = useMemberStore()
            memberStore.clearProfile()
            uni.showToast({
              icon: 'none',
              title: data.msg || '登录状态已过期',
              duration: 2000,
              complete: () => {
                setTimeout(() => {
                  uni.reLaunch({ url: '/pages/login/login' })
                }, 1000)
              },
            })
            reject(res)
            return
          }
          // 2.1 提取核心数据 res.data
          resolve(res.data as Data<T>)
        } else if (res.statusCode === 401) {
          // HTTP 状态码 401 的情况
          const memberStore = useMemberStore()
          memberStore.clearProfile()
          uni.showToast({
            icon: 'none',
            title: '登录状态已过期',
            duration: 2000,
            complete: () => {
              setTimeout(() => {
                uni.reLaunch({ url: '/pages/login/login' })
              }, 1000)
            },
          })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          uni.showToast({
            icon: 'none',
            title: (res.data as Data<T>).msg || '请求失败',
          })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}
