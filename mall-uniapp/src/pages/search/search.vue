<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { searchGoods, getSuggestions, getAggregations } from '@/services/search'
import type { SearchParams, SearchResult, GoodsItem } from '@/services/search'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 搜索关键词
const keyword = ref('')
// 搜索建议列表
const suggestions = ref<string[]>([])
// 搜索结果
const goodsList = ref<GoodsItem[]>([])
// 筛选数据
const brands = ref<Array<{ brandName: string; count: number }>>([])
const categories = ref<Array<{ categoryName: string; count: number }>>([])
const priceRanges = ref<Array<{ text: string; count: number; min: number; max: number }>>([])
// 是否显示搜索结果
const showSearchResult = ref(false)
// 是否显示搜索建议
const showSuggestions = ref(false)
// 是否正在加载
const loading = ref(false)
// 是否加载完成
const finished = ref(false)
// 搜索历史
const historyList = ref<string[]>([])
// 当前页码
const page = ref(1)
// 每页大小
const pageSize = ref(10)
// 是否还有更多数据
const hasMore = ref(true)
// 总记录数
const total = ref(0)

// 热门搜索词
const hotKeywords = ref(['手机', '电脑', '耳机', '手表', '平板', '相机', '零食', '美妆', '服饰'])

// 当前选中的筛选条件
const selectedBrand = ref('')
const selectedCategory = ref('')
const selectedPriceRange = ref('')
// 是否显示筛选弹窗
const showFilterDrawer = ref(false)
// 筛选抽屉内容类型
const filterDrawerType = ref('') // 'brand', 'category', 'price'

// 加载搜索历史
onLoad(() => {
  const history = uni.getStorageSync('searchHistory')
  if (history) {
    historyList.value = JSON.parse(history)
  }
})

// 防抖定时器
let debounceTimer: number | null = null

// 监听输入，获取搜索建议(带防抖)
const onInput = (event: any) => {
  const value = event.detail.value
  keyword.value = value

  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 如果输入为空，清空建议
  if (!value) {
    showSuggestions.value = false
    suggestions.value = []
    return
  }

  // 设置300ms防抖
  debounceTimer = setTimeout(() => {
    fetchSuggestions(value)
  }, 300)
}

// 获取搜索建议
const fetchSuggestions = async (value: string) => {
  try {
    const { data } = await getSuggestions(value)
    suggestions.value = data
    showSuggestions.value = data.length > 0
    showSearchResult.value = false
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    uni.showToast({ title: '获取搜索建议失败', icon: 'none' })
  }
}

// 高亮关键词
const highlightKeyword = (text: string): string => {
  if (!keyword.value) return text
  const regex = new RegExp(keyword.value, 'gi')
  return text.replace(regex, (match) => `<text class="highlight">${match}</text>`)
}

// 选择搜索建议
const onSuggestionSelect = (suggestion: string) => {
  keyword.value = suggestion
  showSuggestions.value = false
  handleSearch()
}

// 保存搜索历史
const saveSearchHistory = (keyword: string) => {
  // 如果已存在，先删除
  const index = historyList.value.findIndex((item) => item === keyword)
  if (index !== -1) {
    historyList.value.splice(index, 1)
  }
  // 添加到最前面
  historyList.value.unshift(keyword)
  // 最多保存10条
  if (historyList.value.length > 10) {
    historyList.value.pop()
  }
  // 保存到本地
  uni.setStorageSync('searchHistory', JSON.stringify(historyList.value))
}

// 清空搜索历史
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        historyList.value = []
        uni.removeStorageSync('searchHistory')
      }
    },
  })
}

// 打开筛选抽屉
const openFilterDrawer = (type: string) => {
  filterDrawerType.value = type
  showFilterDrawer.value = true
}

// 关闭筛选抽屉
const closeFilterDrawer = () => {
  showFilterDrawer.value = false
}

// 品牌选择
const onBrandSelect = (brand: { brandName: string; count: number }) => {
  selectedBrand.value = brand.brandName
  closeFilterDrawer()
  // 重新搜索
  handleSearch()
}

// 分类选择
const onCategorySelect = (category: { categoryName: string; count: number }) => {
  selectedCategory.value = category.categoryName
  closeFilterDrawer()
  // 重新搜索
  handleSearch()
}

// 价格区间选择
const onPriceRangeSelect = (range: { text: string; count: number; min: number; max: number }) => {
  selectedPriceRange.value = range.text
  closeFilterDrawer()
  // 重新搜索
  handleSearch()
}

// 清除筛选条件
const clearFilters = () => {
  selectedBrand.value = ''
  selectedCategory.value = ''
  selectedPriceRange.value = ''
  // 重新搜索
  handleSearch()
}

// 搜索商品
const handleSearch = async () => {
  if (
    !keyword.value.trim() &&
    !selectedBrand.value &&
    !selectedCategory.value &&
    !selectedPriceRange.value
  ) {
    uni.showToast({ title: '请输入搜索关键词或选择筛选条件', icon: 'none' })
    return
  }

  // 保存搜索历史
  if (keyword.value.trim()) {
    saveSearchHistory(keyword.value)
  }

  // 重置页码和加载状态
  page.value = 1
  loading.value = true
  finished.value = false
  hasMore.value = true
  goodsList.value = []

  // 执行搜索
  await loadGoodsList()
  // 获取聚合信息
  await loadAggregations()
}

// 加载商品列表
const loadGoodsList = async () => {
  try {
    console.log('加载商品列表')
    loading.value = true
    const params: SearchParams = {
      keyword: keyword.value,
      page: page.value,
      size: pageSize.value,
    }

    // 添加筛选条件
    if (selectedBrand.value) {
      params.brandName = selectedBrand.value
    }
    if (selectedCategory.value) {
      params.categoryName = selectedCategory.value
    }
    if (selectedPriceRange.value) {
      const priceRange = priceRanges.value.find((p) => p.text === selectedPriceRange.value)
      if (priceRange) {
        params.minPrice = priceRange.min
        params.maxPrice = priceRange.max
      }
    }

    const { data } = await searchGoods(params)

    // 更新总数
    total.value = data.total

    // 处理返回的商品列表（兼容goodsDtoList和goodsVOList）
    const resultList = data.goodsVOList || data.goodsDtoList || []
    
    // 处理价格（从分转为元）
    const processedList = resultList.map(item => ({
      ...item,
      goodsPrice: (item.goodsPrice / 100).toFixed(2) // 将分转换为元，并保留两位小数
    }))

    // 追加结果
    goodsList.value = page.value === 1 ? processedList : [...goodsList.value, ...processedList]

    // 判断是否还有更多
    hasMore.value = goodsList.value.length < total.value

    // 更新完成状态
    finished.value = !hasMore.value
    showSearchResult.value = true
    showSuggestions.value = false
  } catch (error) {
    console.error('搜索商品失败:', error)
    uni.showToast({ title: '搜索商品失败', icon: 'none' })
  } finally {
    loading.value = false
  }
}

// 加载聚合信息
const loadAggregations = async () => {
  try {
    const params: SearchParams = {
      keyword: keyword.value,
      page: 1,
      size: 1,
    }

    const { data } = await getAggregations(params)
    // 更新筛选条件
    brands.value = data.品牌.map((name: string) => ({
      brandName: name,
      count: 0, // 后端没有返回数量，暂时设为0
    }))
    categories.value = data.分类.map((name: string) => ({
      categoryName: name,
      count: 0, // 后端没有返回数量，暂时设为0
    }))
    priceRanges.value = data.价格区间.map((text: string) => ({
      text,
      count: 0, // 后端没有返回数量，暂时设为0
      min: parseFloat(text.split(' - ')[0].replace('¥', '')),
      max: parseFloat(text.split(' - ')[1].replace('¥', '')),
    }))
  } catch (error) {
    console.error('获取聚合信息失败:', error)
  }
}

// 加载更多
const loadMore = () => {
  if (loading.value || !hasMore.value) return
  page.value++
  loadGoodsList()
}

// 点击搜索历史或热门搜索
const onKeywordClick = (word: string) => {
  keyword.value = word
  handleSearch()
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 清空搜索框
const clearKeyword = () => {
  keyword.value = ''
  showSearchResult.value = false
  showSuggestions.value = false
}

// 是否显示清空按钮
const showClear = computed(() => keyword.value.length > 0)
</script>

<template>
  <view class="search-page">
    <!-- 顶部导航栏 -->
    <view class="navbar" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
      <!-- 搜索条 -->
      <view class="search-bar-container">
        <view class="search-input">
          <text class="iconfont icon-search"></text>
          <input
            type="text"
            v-model="keyword"
            confirm-type="search"
            placeholder="请输入搜索关键词"
            @input="onInput"
            @confirm="handleSearch"
            focus
          />
          <view v-if="showClear" class="clear-btn" @tap="clearKeyword">
            <text class="iconfont icon-close" style="font-size: 24rpx; color: var(--gray-7);"></text>
          </view>
        </view>
        <view class="cancel-btn" @tap="goBack">取消</view>
      </view>
    </view>

    <!-- 搜索建议 -->
    <scroll-view
      scroll-y
      class="search-suggestions"
      v-if="showSuggestions && suggestions.length > 0"
    >
      <view
        class="suggestion-item"
        v-for="(item, index) in suggestions"
        :key="index"
        @tap="onSuggestionSelect(item)"
      >
        <text class="iconfont icon-search" style="font-size: 32rpx; color: var(--gray-5);"></text>
        <text class="suggestion-text">{{ item }}</text>
      </view>
    </scroll-view>

    <!-- 搜索历史和热门搜索 -->
    <scroll-view scroll-y class="search-content" v-if="!showSearchResult && !showSuggestions">
      <!-- 搜索历史 -->
      <view class="search-history" v-if="historyList.length > 0">
        <view class="section-header">
          <text class="section-title">搜索历史</text>
          <view class="clear-btn" @tap="clearHistory">
            <text class="iconfont icon-delete" style="font-size: 26rpx; color: var(--gray-5);"></text>
            <text>清空</text>
          </view>
        </view>
        <view class="tags-container">
          <view
            class="tag-item"
            v-for="(item, index) in historyList"
            :key="index"
            @tap="onKeywordClick(item)"
          >
            {{ item }}
          </view>
        </view>
      </view>

      <!-- 热门搜索 -->
      <view class="hot-search">
        <view class="section-header">
          <text class="section-title">热门搜索</text>
        </view>
        <view class="tags-container">
          <view
            class="tag-item"
            v-for="(item, index) in hotKeywords"
            :key="index"
            @tap="onKeywordClick(item)"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 搜索结果 -->
    <scroll-view scroll-y class="search-result" v-if="showSearchResult" @scrolltolower="loadMore">
      <!-- 筛选条件栏 -->
      <view class="filter-bar">
        <view
          class="filter-item"
          :class="{ active: selectedBrand }"
          @tap="openFilterDrawer('brand')"
        >
          <text class="filter-text">{{ selectedBrand || '品牌' }}</text>
          <text class="iconfont icon-arrow-down" style="font-size: 24rpx;"></text>
        </view>
        <view
          class="filter-item"
          :class="{ active: selectedCategory }"
          @tap="openFilterDrawer('category')"
        >
          <text class="filter-text">{{ selectedCategory || '分类' }}</text>
          <text class="iconfont icon-arrow-down"></text>
        </view>
        <view
          class="filter-item"
          :class="{ active: selectedPriceRange }"
          @tap="openFilterDrawer('price')"
        >
          <text class="filter-text">{{ selectedPriceRange || '价格' }}</text>
          <text class="iconfont icon-arrow-down"></text>
        </view>
        <view
          class="filter-item"
          v-if="selectedBrand || selectedCategory || selectedPriceRange"
          @tap="clearFilters"
        >
          <text class="filter-text">清除</text>
        </view>
      </view>

      <!-- 搜索结果数量 -->
      <view class="result-count" v-if="total > 0">
        共找到 <text class="count-number">{{ total }}</text> 件相关商品
      </view>

      <!-- 商品列表 -->
      <view class="goods-list">
        <navigator
          class="goods-item"
          v-for="item in goodsList"
          :key="item.goodsId"
          :url="`/pages/goods/goods?id=${item.goodsId}`"
        >
          <image class="goods-image" :src="item.goodsImg" mode="aspectFill"></image>
          <view class="goods-info">
            <rich-text class="goods-name" :nodes="highlightKeyword(item.goodsName)"></rich-text>
            <view class="goods-meta">
              <view class="price-wrap">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ item.goodsPrice }}</text>
              </view>
              <text class="brand-name">{{ item.brandName }}</text>
            </view>
            <view class="goods-category">{{ item.categoryName }}</view>
          </view>
        </navigator>
      </view>

      <!-- 加载状态 -->
      <view class="loading-state">
        <view class="loading-icon" v-if="loading">
          <text class="iconfont icon-loading" style="font-size: 48rpx; color: var(--primary-color);"></text>
          <text class="loading-text">加载中...</text>
        </view>
        <text v-else-if="finished && goodsList.length > 0" class="end-text"> ~ 已经到底啦 ~ </text>
        <view v-else-if="finished && goodsList.length === 0" class="empty-state">
          <text class="iconfont icon-empty" style="font-size: 96rpx; color: var(--gray-3);"></text>
          <text class="empty-text">暂无相关商品</text>
        </view>
      </view>
    </scroll-view>

    <!-- 筛选抽屉 -->
    <view class="filter-drawer-mask" v-if="showFilterDrawer" @tap="closeFilterDrawer"></view>
    <view class="filter-drawer" v-if="showFilterDrawer">
      <view class="drawer-header">
        <text class="drawer-title">
          {{
            filterDrawerType === 'brand'
              ? '选择品牌'
              : filterDrawerType === 'category'
              ? '选择分类'
              : '选择价格区间'
          }}
        </text>
        <text class="drawer-close" @tap="closeFilterDrawer">×</text>
      </view>
      <scroll-view scroll-y class="drawer-content">
        <!-- 品牌列表 -->
        <view class="drawer-list" v-if="filterDrawerType === 'brand'">
          <view
            class="drawer-item"
            v-for="brand in brands"
            :key="brand.brandName"
            :class="{ active: selectedBrand === brand.brandName }"
            @tap="onBrandSelect(brand)"
          >
            {{ brand.brandName }}
          </view>
        </view>

        <!-- 分类列表 -->
        <view class="drawer-list" v-if="filterDrawerType === 'category'">
          <view
            class="drawer-item"
            v-for="category in categories"
            :key="category.categoryName"
            :class="{ active: selectedCategory === category.categoryName }"
            @tap="onCategorySelect(category)"
          >
            {{ category.categoryName }}
          </view>
        </view>

        <!-- 价格区间列表 -->
        <view class="drawer-list" v-if="filterDrawerType === 'price'">
          <view
            class="drawer-item"
            v-for="range in priceRanges"
            :key="range.text"
            :class="{ active: selectedPriceRange === range.text }"
            @tap="onPriceRangeSelect(range)"
          >
            {{ range.text }}
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style lang="scss">
// 导入字体图标 - 使用更新的图标库
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/c/font_4379848_3oigc4ykjg6.woff2?t=20250812') format('woff2'),
    url('//at.alicdn.com/t/c/font_4379848_3oigc4ykjg6.woff?t=20250812') format('woff'),
    url('//at.alicdn.com/t/c/font_4379848_3oigc4ykjg6.ttf?t=20250812') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 24rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-search:before {
  content: '\e651';
}
.icon-close:before {
  content: '\e646';
}
.icon-delete:before {
  content: '\e67e';
}
.icon-loading:before {
  content: '\e61f';
}
.icon-empty:before {
  content: '\e6a6';
}
.icon-arrow-down:before {
  content: '\e6b9';
}

page {
  height: 100%;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --primary-color: #4080ff;
  --gray-0: #ffffff;
  --gray-1: #f8f8f8;
  --gray-2: #f2f2f2;
  --gray-3: #e8e8e8;
  --gray-5: #999999;
  --gray-7: #666666;
  --gray-9: #333333;
  --price-color: #ff2c45;
}

.search-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.navbar {
  background-color: var(--primary-color);
  padding: 0 30rpx;
  position: relative;
  z-index: 10;
  box-sizing: border-box;

  .search-bar-container {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    width: 100%;

    .search-input {
      flex: 1;
      display: flex;
      align-items: center;
      height: 72rpx;
      background-color: var(--gray-0);
      border-radius: 36rpx;
      padding: 0 20rpx;
      margin-right: 20rpx;
      position: relative;

      .icon-search {
        font-size: 36rpx;
        color: var(--gray-5);
        padding: 0 16rpx 0 10rpx;
      }

      input {
        flex: 1;
        height: 72rpx;
        font-size: 28rpx;
        color: var(--gray-9);
      }

      .clear-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        border-radius: 24rpx;
        background-color: var(--gray-3);

        .icon-close {
          font-size: 24rpx;
          color: var(--gray-7);
        }
      }
    }

    .cancel-btn {
      font-size: 30rpx;
      color: var(--gray-0);
      padding: 10rpx 15rpx;
      min-width: 80rpx;
      text-align: center;
    }
  }
}

.search-content {
  flex: 1;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;

  .section-title {
    font-size: 30rpx;
    color: var(--gray-9);
    font-weight: 500;
  }

  .clear-btn {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: var(--gray-5);

    .iconfont {
      font-size: 26rpx;
      margin-right: 6rpx;
    }
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 40rpx;

  .tag-item {
    padding: 12rpx 24rpx;
    background-color: var(--gray-0);
    border-radius: 28rpx;
    font-size: 26rpx;
    color: var(--gray-7);
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    transition: all 0.2s;

    &:active {
      background-color: var(--gray-2);
    }
  }
}

.search-history {
  margin-bottom: 40rpx;
}

.search-suggestions {
  position: absolute;
  top: calc(140rpx + var(--status-bar-height, 0px));
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-0);
  z-index: 9;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .suggestion-item {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;
    font-size: 28rpx;
    color: var(--gray-9);
    border-bottom: 1px solid var(--gray-2);
    transition: all 0.2s;

    .iconfont {
      font-size: 32rpx;
      color: var(--gray-5);
      margin-right: 20rpx;
    }

    .suggestion-text {
      flex: 1;
    }

    &:active {
      background-color: var(--gray-1);
    }
  }
}

.search-result {
  flex: 1;
  background-color: var(--gray-1);
}

.filter-bar {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: var(--gray-0);
  border-bottom: 1px solid var(--gray-2);
  position: sticky;
  top: 0;
  z-index: 5;

  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 28rpx;
    color: var(--gray-7);
    position: relative;

    &.active {
      color: var(--primary-color);
      font-weight: 500;
    }

    .filter-text {
      max-width: 150rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .iconfont {
      margin-left: 4rpx;
      font-size: 24rpx;
      transition: transform 0.2s;
    }

    &.active .iconfont {
      transform: rotate(180deg);
    }
  }
}

.result-count {
  padding: 20rpx 30rpx;
  font-size: 24rpx;
  color: var(--gray-5);
  background-color: var(--gray-0);

  .count-number {
    color: var(--primary-color);
    font-weight: 500;
  }
}

.goods-list {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .goods-item {
    width: calc(50% - 10rpx);
    background-color: var(--gray-0);
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.2s;

    &:active {
      transform: scale(0.98);
    }

    .goods-image {
      width: 100%;
      height: 345rpx;
      background-color: var(--gray-1);
    }

    .goods-info {
      padding: 16rpx;

      .goods-name {
        font-size: 28rpx;
        color: var(--gray-9);
        line-height: 1.4;
        height: 78rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-bottom: 12rpx;
      }

      .goods-meta {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-bottom: 6rpx;

        .price-wrap {
          display: flex;
          align-items: baseline;

          .price-symbol {
            font-size: 24rpx;
            color: var(--price-color);
            margin-right: 2rpx;
          }

          .price-value {
            font-size: 36rpx;
            color: var(--price-color);
            font-weight: 600;
          }
        }

        .brand-name {
          font-size: 22rpx;
          color: var(--gray-5);
          max-width: 120rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .goods-category {
        font-size: 22rpx;
        color: var(--gray-5);
        padding: 2rpx 10rpx;
        background-color: var(--gray-1);
        border-radius: 4rpx;
        display: inline-block;
      }
    }
  }
}

.loading-state {
  text-align: center;
  padding: 40rpx 0;
  color: var(--gray-5);

  .loading-icon {
    display: flex;
    flex-direction: column;
    align-items: center;

    .iconfont {
      font-size: 48rpx;
      margin-bottom: 10rpx;
      animation: rotate 1s linear infinite;
    }

    .loading-text {
      font-size: 26rpx;
    }
  }

  .end-text {
    font-size: 26rpx;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 80rpx 0;

    .iconfont {
      font-size: 96rpx;
      margin-bottom: 20rpx;
      color: var(--gray-3);
    }

    .empty-text {
      font-size: 28rpx;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.highlight {
  color: var(--primary-color);
  font-weight: bold;
}

.filter-drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.filter-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60vh;
  background-color: var(--gray-0);
  z-index: 101;
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s;

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid var(--gray-2);

    .drawer-title {
      font-size: 32rpx;
      font-weight: 500;
      color: var(--gray-9);
    }

    .drawer-close {
      font-size: 40rpx;
      color: var(--gray-7);
      padding: 10rpx;
    }
  }

  .drawer-content {
    height: calc(60vh - 90rpx);

    .drawer-list {
      padding: 20rpx 0;

      .drawer-item {
        padding: 24rpx 30rpx;
        font-size: 28rpx;
        color: var(--gray-7);
        border-bottom: 1px solid var(--gray-2);

        &.active {
          color: var(--primary-color);
          background-color: rgba(64, 128, 255, 0.05);
        }

        &:active {
          background-color: var(--gray-1);
        }
      }
    }
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
