<script setup lang="ts">
import { getSeckillGoodsDetail, getSeckillGoodsList } from '@/services/seckill/goods'
import { onLoad } from '@dcloudio/uni-app'
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 定义商品数据类型
interface GoodsItem {
  goodsId: number
  goodsName: string
  goodsDesc?: string
  goodsImg?: string
  goodsPrice?: number
  originalPrice?: number // 原价
  stock?: number // 库存
  soldCount?: number // 已售数量
  brandId?: number
  brandName?: string
  categoryId?: number
  categoryName?: string
}

// 活动ID
const activityId = ref<number>(0)
// 活动名称
const activityName = ref('')
// 商品列表
const productList = ref<GoodsItem[]>([])
// 是否正在加载
const loading = ref(false)
// 秒杀活动状态: 0-未开始, 1-进行中, 2-已结束
const activityStatus = ref(1)
// 倒计时
const countdown = ref({
  hours: '00',
  minutes: '00',
  seconds: '00',
})
// 倒计时定时器
let timer: number | null = null
// 活动结束时间（模拟数据）
const endTime = ref(new Date().getTime() + 24 * 60 * 60 * 1000) // 默认24小时后结束

// 分页相关参数
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const scrollLoading = ref(false)
const total = ref(0) // 添加总数记录

// SKU弹窗相关
const isShowSku = ref(false)
const mode = ref<string | number>('buyNow')
const skuPopupRef = ref(null)
const currentGoods = ref<GoodsItem | null>(null)

// SKU组件数据
const localdata = ref({
  _id: 0,
  goodsName: '',
  goods_thumb: '',
  spec_list: [] as any[],
  sku_list: [] as any[],
})

// 计算属性：是否有商品
const hasProducts = computed(() => productList.value.length > 0)

// 更新倒计时
const updateCountdown = () => {
  const now = new Date().getTime()
  const diff = endTime.value - now

  if (diff <= 0) {
    // 根据活动状态判断
    if (activityStatus.value === 0) {
      // 未开始状态下倒计时结束，应该变为进行中
      activityStatus.value = 1
      // 尝试重新加载商品列表
      getProductList()
    } else if (activityStatus.value === 1) {
      // 进行中状态下倒计时结束，应该变为已结束
      activityStatus.value = 2
    }

    countdown.value = { hours: '00', minutes: '00', seconds: '00' }
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    return
  }

  // 计算小时、分钟、秒
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)

  countdown.value = {
    hours: hours < 10 ? `0${hours}` : `${hours}`,
    minutes: minutes < 10 ? `0${minutes}` : `${minutes}`,
    seconds: seconds < 10 ? `0${seconds}` : `${seconds}`,
  }
}

// 开始倒计时
const startCountdown = () => {
  updateCountdown()
  timer = setInterval(updateCountdown, 1000)
}

// 获取秒杀商品列表
const getProductList = async (isLoadMore = false) => {
  // 如果是加载更多，但已经没有更多数据，则直接返回
  if (isLoadMore && !hasMore.value) {
    return
  }

  // 如果是加载更多，设置scrollLoading，否则设置整体loading
  if (isLoadMore) {
    scrollLoading.value = true
  } else {
    loading.value = true
  }

  try {
    const res = await getSeckillGoodsList(pageNum.value, pageSize.value, activityId.value)

    // 处理后端返回的数据结构
    let goodsList: any[] = []

    if (res && res.code === 200) {
      // 处理后端返回的数据结构 {code: 200, data: {total: 11, goodsDtoList: [...]}}
      if (res.data && res.data.goodsDtoList) {
        goodsList = res.data.goodsDtoList || []

        // 更新总数，用于分页判断
        total.value = res.data.total || 0
        hasMore.value = productList.value.length + goodsList.length < total.value
      } else {
        // 如果没有数据，设置为没有更多
        hasMore.value = false
        total.value = 0
      }

      // 处理商品数据，使用后端返回的实际字段
      const processedGoodsList = goodsList.map((item: any) => ({
        ...item,
        // 使用后端返回的秒杀价格作为商品价格
        goodsPrice: item.minSecKillPrice || 0,
        // 使用后端返回的原价
        originalPrice: item.minOriginalPrice || 0,
        // 使用后端返回的秒杀库存
        stock: item.secKillStock || 0,
        // 使用后端返回的已售数量
        soldCount: item.soldCount || 0,
        // 确保图片字段存在
        goodsImg: item.goodsImg || '',
        // 确保商品名称存在
        goodsName: item.goodsName || '未知商品',
        // 确保商品ID存在
        goodsId: item.goodsId || 0,
        // 添加折扣率显示
        discountRate: item.discountRate || 0,
      }))

      // 如果是加载更多，拼接数据，否则替换数据
      if (isLoadMore) {
        productList.value = [...productList.value, ...processedGoodsList]
      } else {
        productList.value = processedGoodsList
      }
    } else {
      if (!isLoadMore) {
        productList.value = []
      }
      hasMore.value = false
      total.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch seckill goods:', error)
    if (!isLoadMore) {
      productList.value = []
    }
    hasMore.value = false
    total.value = 0
  } finally {
    if (isLoadMore) {
      scrollLoading.value = false
    } else {
      loading.value = false
    }
  }
}

// 加载更多数据
const loadMore = () => {
  if (loading.value || scrollLoading.value || !hasMore.value) return

  pageNum.value++
  getProductList(true)
}

// 监听页面滚动到底部
const onReachBottom = () => {
  loadMore()
}

// 下拉刷新
const onPullDownRefresh = () => {
  // 重置页码和状态
  pageNum.value = 1
  hasMore.value = true
  total.value = 0

  // 重新加载数据
  getProductList()

  // 停止下拉刷新动画
  uni.stopPullDownRefresh()
}

// 计算折扣率
const getDiscount = (original: number | undefined, current: number | undefined) => {
  if (!original || !current || original <= 0) return '0'
  const discount = Math.floor((1 - current / original) * 100)
  return discount.toString()
}

// 打开规格选择弹窗
const openSkuPopup = async (item: GoodsItem) => {
  try {
    const res = await getSeckillGoodsDetail(item.goodsId)
    currentGoods.value = item

    // 判断商品是否有库存
    const hasStock = item.stock && item.stock > 0

    // 获取SKU弹窗组件所需要的商品SKU信息
    localdata.value = {
      // 商品id，它们是vk-data-goods-sku-popup.d.ts中SkuPopupLocaldata的属性名
      _id: res.data.id,
      // name为商品名称
      goodsName: res.data.goodsName,
      // goods_thumb为商品图片，这里mainImg[0]获取主图中的第一张。
      goods_thumb: res.data.mainImg[0],
      // specs为商品的可选规格属性集合，具体属性参考vk-data-goods-sku-popup.d.ts中的AttrItem
      // map()数组的映射方法。使用map方法取出数据映射到AttrItem中的name和values属性再进行返回
      spec_list: res.data.specs.map((v) => {
        return {
          // 映射时可以钉起vk-data-goods-sku-popup.d.中的AttrItem
          // name为AttrItem类型的属性名，为规格属性名称
          name: v.name,
          // values规格属性值集合
          list: v.values,
        }
      }),
      // 商品的SKU列表，skus为SkuItem类型。
      // 这里也需要使用map()进行重新映射下属性
      sku_list: res.data.skus.map((v) => {
        // nameArr用来保存当前SKU的规格属性值字符串数组
        let nameArr: string[] = []
        // v.specs得到AttrItem[]规格属性列表，使用map()对其遍历，v.values取出每个属性的属性值列表
        v.specs.map((v) => {
          // v为AttrItem规格属性对象
          v.values.map((val) => {
            // val为AttrValues规格属性值对象
            // val.name得到规格属性值，调用push()方法将多个属性值添加到nameArr数组中
            nameArr.push(val.name)
          })
        })
        return {
          // 映射时可以钉起vk-data-goods-sku-popup.d.中的SkuPopupSkuItem
          _id: v.id,
          // 商品id
          goods_id: res.data.id,
          // 商品名称
          goods_name: res.data.goodsName,
          // 商品的单品图书
          image: v.picture,
          // 商品的价格
          price: v.price * 100,
          // sku库存
          stock: v.inventory,
          // SKU 的规格属性列表
          sku_name_arr: nameArr,
          // 添加秒杀商品ID
          seckillId: v.seckillId,
        }
      }),
    }

    // 如果没有库存，显示提示
    if (!hasStock) {
      uni.showToast({
        title: '商品已抢光',
        icon: 'none',
      })
      return
    }

    // 设置模式（秒杀应使用立即购买模式）
    mode.value = 3

    // 显示SKU弹窗
    setTimeout(() => {
      isShowSku.value = true
    }, 100)
  } catch (error) {
    console.error('打开规格选择弹窗失败:', error)
    uni.showToast({
      title: '打开规格选择失败',
      icon: 'none',
    })
  }
}

// 立即购买事件处理
const onBuyNow = async (data: any) => {
  // 获取SKU信息，有可能直接返回SKU对象
  const selectInfo = typeof data === 'object' && data._id ? data : data.selectShopItemInfo || {}

  // 检查是否有库存
  if (selectInfo && selectInfo.stock <= 0) {
    uni.showToast({
      title: '所选规格已抢光',
      icon: 'none',
    })
    return
  }

  // 从本地数据中获取规格信息
  const specList = localdata.value.spec_list || []
  const skuList = localdata.value.sku_list || []

  // 找到当前选中的SKU
  const currentSku = skuList.find((sku: any) => sku._id === selectInfo._id) || {}

  // 获取规格文本数组
  let specTexts: string[] = []

  // 尝试从SKU名称数组获取规格信息
  if (currentSku.sku_name_arr && currentSku.sku_name_arr.length > 0) {
    specTexts = currentSku.sku_name_arr
  }
  // 如果数据中有selectArr，使用它
  else if (data.selectArr && data.selectArr.length > 0) {
    specTexts = data.selectArr
  }
  // 否则尝试从规格列表构建
  else if (specList.length > 0) {
    // 尝试从规格列表找出可能的规格值
    specList.forEach((spec: any) => {
      if (spec.name && spec.list && spec.list.length > 0) {
        // 简单示例，实际应该匹配已选规格
        specTexts.push(`${spec.name}: ${spec.list[0]?.name || '默认'}`)
      }
    })
  }

  // 构建订单所需的商品数据
  const goodsItem = {
    id: selectInfo._id || 0, // SKU ID
    goodsId: currentGoods.value?.goodsId || 0, // 商品ID
    name: currentGoods.value?.goodsName || selectInfo.goods_name || '', // 商品名称
    picture: selectInfo.image || currentGoods.value?.goodsImg || '', // 商品图片
    count: data.buy_num || 1, // 购买数量
    nowPrice: selectInfo.price / 100 || 0, // 当前价格，从分转为元
    price: selectInfo.price / 100 || 0, // 价格，从分转为元
    attrsText: specTexts.join('，'), // 规格文本
    skuId: selectInfo._id || 0, // SKU ID
    selected: true, // 选中状态
    stock: selectInfo.stock || 0, // 库存
    // 秒杀标记
    activityId: activityId.value,
    activityType: 'seckill',
    secKillGoodsId: skuList.find((sku) => sku._id === selectInfo._id)?.seckillId || '', // 从SKU列表中获取seckillId
    // 规格详情
    specs: specTexts,
    // 商品完整数据
    productData: {
      product: currentGoods.value,
      sku: selectInfo,
      specList: specList,
      specTexts: specTexts,
    },
  }

  // 计算总价
  const totalPrice = (goodsItem.nowPrice * goodsItem.count).toFixed(2)

  // 商品数据序列化为JSON字符串
  const cartList = JSON.stringify([goodsItem])

  setTimeout(() => {
    isShowSku.value = false

    // 显示提示信息
    uni.showModal({
      title: '安全提示',
      content: '为了确保秒杀安全，下单时需要进行滑块验证，请耐心等待',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 跳转到订单创建页面，传递活动ID和秒杀商品ID
        uni.navigateTo({
          url: `/pagesOrder/create/create?totalPrice=${totalPrice}&cartList=${cartList}&orderType=seckill&activityId=${activityId.value}&secKillGoodsId=${goodsItem.secKillGoodsId}`,
          success: () => {
            console.log('跳转到订单页面成功')
          },
          fail: (err) => {
            console.error('跳转失败:', err)
            // 失败后尝试备用路径
            uni.navigateTo({
              url: `/pages/order/create?totalPrice=${totalPrice}&cartList=${cartList}&orderType=seckill&activityId=${activityId.value}&secKillGoodsId=${goodsItem.secKillGoodsId}`,
            })
          },
        })
      },
    })
  }, 800)
}

// 监听页面加载，获取路由参数
onLoad((options: any) => {
  if (options && options.activityId) {
    activityId.value = parseInt(options.activityId)
  }
  if (options && options.activityName) {
    activityName.value = decodeURIComponent(options.activityName)
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: activityName.value || '秒杀活动',
    })
  }

  // 获取活动状态
  if (options && options.status !== undefined) {
    activityStatus.value = parseInt(options.status)
  }

  // 获取活动的开始和结束时间
  if (options && options.startTime) {
    const startTime = decodeURIComponent(options.startTime)
    // 如果活动未开始，设置倒计时为开始时间
    if (activityStatus.value === 0 && startTime) {
      endTime.value = new Date(startTime).getTime()
    }
  }

  if (options && options.endTime) {
    const endTimeStr = decodeURIComponent(options.endTime)
    // 如果活动已开始，设置倒计时为结束时间
    if (activityStatus.value === 1 && endTimeStr) {
      endTime.value = new Date(endTimeStr).getTime()
    }
  }

  // 加载商品列表
  getProductList()
})

onMounted(() => {
  startCountdown()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<template>
  <view class="container">
    <!-- 秒杀活动头部 -->
    <view class="seckill-header">
      <view class="seckill-title">
        <text class="title-text">{{ activityName || '限时秒杀' }}</text>
        <view
          class="seckill-badge"
          :class="{ ended: activityStatus === 2, upcoming: activityStatus === 0 }"
        >
          {{ activityStatus === 0 ? '即将开始' : activityStatus === 1 ? '抢购中' : '已结束' }}
        </view>
      </view>

      <!-- 倒计时 -->
      <view class="countdown-container" v-if="activityStatus !== 2">
        <text class="countdown-label">{{ activityStatus === 0 ? '距开始' : '距结束' }}</text>
        <view class="countdown-box">
          <text class="countdown-time">{{ countdown.hours }}</text>
          <text class="countdown-divider">:</text>
          <text class="countdown-time">{{ countdown.minutes }}</text>
          <text class="countdown-divider">:</text>
          <text class="countdown-time">{{ countdown.seconds }}</text>
        </view>
      </view>
    </view>

    <!-- 活动商品列表 -->
    <block v-if="hasProducts">
      <view class="products-header">
        <text class="products-title">{{ activityName || '秒杀商品' }}</text>
        <text class="products-count">共 {{ total }} 件</text>
      </view>

      <view class="products-list">
        <view
          class="product-item"
          :class="{ 'sold-out': !item.stock || item.stock <= 0 }"
          v-for="item in productList"
          :key="item.goodsId"
        >
          <navigator
            class="product-image-container"
            :url="`/pages/goods/goods?id=${item.goodsId}&activityId=${activityId}&activityType=seckill`"
          >
            <view class="product-image" v-if="item.goodsImg">
              <image :src="item.goodsImg" mode="aspectFill"></image>
            </view>
            <view class="discount-badge" v-if="item.goodsPrice && item.originalPrice">
              {{ getDiscount(item.originalPrice, item.goodsPrice) }}折
            </view>
          </navigator>

          <view class="product-info">
            <navigator
              :url="`/pages/goods/goods?id=${item.goodsId}&activityId=${activityId}&activityType=seckill`"
            >
              <view class="product-name">{{ item.goodsName }}</view>
              <view class="product-meta">
                <text class="brand" v-if="item.brandName">{{ item.brandName }}</text>
                <text class="category" v-if="item.categoryName">{{ item.categoryName }}</text>
              </view>
              <view class="product-desc" v-if="item.goodsDesc">{{ item.goodsDesc }}</view>
            </navigator>

            <!-- 库存进度条 -->
            <view
              class="stock-container"
              v-if="item.stock !== undefined && item.soldCount !== undefined"
            >
              <view class="stock-progress-bg">
                <view
                  class="stock-progress"
                  :style="{
                    width: `${Math.min(
                      100,
                      (item.soldCount / (item.soldCount + item.stock)) * 100,
                    )}%`,
                  }"
                ></view>
              </view>
              <text class="stock-text">已抢{{ item.soldCount }}件 仅剩{{ item.stock }}件</text>
            </view>

            <view class="product-price">
              <view class="price-container">
                <text class="seckill-price">
                  <text class="small">¥</text>{{ item.goodsPrice || '暂无价格' }}
                </text>
                <text class="original-price" v-if="item.originalPrice"
                  >¥{{ item.originalPrice }}</text
                >
              </view>

              <!-- 快速购买按钮 -->
              <view class="action-buttons">
                <view
                  class="btn-buy"
                  :class="{ 'btn-sold-out': !item.stock || item.stock <= 0 }"
                  @tap.stop="openSkuPopup(item)"
                >
                  {{ !item.stock || item.stock <= 0 ? '已抢光' : '立即抢购' }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <view class="loading-text" v-if="scrollLoading">加载中...</view>
        <view class="loading-text" v-else @tap="loadMore">点击加载更多</view>
      </view>
      <view class="load-more" v-else-if="productList.length > 0">
        <view class="loading-text">已加载全部 {{ total }} 件商品</view>
      </view>
    </block>

    <view class="loading-container" v-else-if="loading">
      <view class="loading-text">加载中...</view>
    </view>

    <view class="empty-container" v-else>
      <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
      <text class="empty-text">暂无活动商品</text>
    </view>

    <!-- 回到顶部 -->
    <view
      class="back-to-top"
      @tap="
        () => {
          uni.pageScrollTo({ scrollTop: 0, duration: 300 })
        }
      "
    >
      <text class="back-to-top-text">顶部</text>
    </view>

    <!-- 规格选择弹窗 -->
    <vk-data-goods-sku-popup
      v-model="isShowSku"
      :localdata="localdata"
      :mode="mode"
      add-cart-background-color="#FFA868"
      buy-now-background-color="#3c69dc"
      :is-show-buy-now="true"
      ref="skuPopupRef"
      @buy-now="onBuyNow"
    />
  </view>
</template>

<style lang="scss">
page {
  background-color: #f4f4f4;
}

.container {
  min-height: 100vh;
  padding-bottom: 60rpx;
}

/* 秒杀头部样式 */
.seckill-header {
  background: linear-gradient(to right, #ff6b6b, #cf4444);
  padding: 30rpx 20rpx;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.seckill-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.seckill-badge {
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;

  &.ended {
    background-color: rgba(0, 0, 0, 0.3);
  }

  &.upcoming {
    background-color: #ff9500;
  }
}

.countdown-container {
  display: flex;
  align-items: center;
}

.countdown-label {
  font-size: 26rpx;
  margin-right: 10rpx;
}

.countdown-box {
  display: flex;
  align-items: center;
}

.countdown-time {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.countdown-divider {
  margin: 0 8rpx;
  font-weight: bold;
  font-size: 28rpx;
}

.products-header {
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.products-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.5;
  padding-left: 20rpx;
  border-left: 6rpx solid #cf4444;
}

.products-count {
  font-size: 24rpx;
  color: #999;
}

.products-list {
  padding: 0 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-item {
  width: calc(50% - 10rpx);
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  padding: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  // 添加售罄状态
  &.sold-out {
    position: relative;

    &::after {
      content: '已抢光';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;
      border-radius: 10rpx;
    }
  }
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 220rpx;
  margin-bottom: 15rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.discount-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #cf4444;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-bottom-left-radius: 8rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  font-weight: bold;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  min-height: 80rpx;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  margin: 6rpx 0;
  font-size: 20rpx;

  .brand,
  .category {
    color: #666;
    margin-right: 10rpx;
    margin-bottom: 6rpx;
    padding: 2rpx 8rpx;
    background-color: #f5f5f5;
    border-radius: 4rpx;
  }
}

.product-desc {
  font-size: 24rpx;
  color: #666;
  margin: 10rpx 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.stock-container {
  margin: 15rpx 0;
}

.stock-progress-bg {
  height: 10rpx;
  background-color: #f1f1f1;
  border-radius: 5rpx;
  overflow: hidden;
}

.stock-progress {
  height: 100%;
  background: linear-gradient(to right, #ff6b6b, #cf4444);
  border-radius: 5rpx;
}

.stock-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.product-price {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}

.price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}

.seckill-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #cf4444;

  .small {
    font-size: 22rpx;
    font-weight: normal;
  }
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 15rpx;
}

.action-buttons {
  display: flex;
}

.btn-buy {
  flex: 1;
  background-color: #cf4444;
  color: #fff;
  padding: 12rpx 0;
  text-align: center;
  font-size: 26rpx;
  border-radius: 8rpx;
  font-weight: bold;
}

.loading-container {
  padding: 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 0;
}

.empty-container {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9;
}

.back-to-top-text {
  font-size: 24rpx;
}

// 售罄状态的按钮样式
.btn-sold-out {
  background-color: #ccc !important;
  color: #fff;
  cursor: not-allowed;
}

/* 添加加载更多样式 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  line-height: 60rpx;
}
</style>
